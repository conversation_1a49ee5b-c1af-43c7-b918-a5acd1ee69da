// ТИМЧАСОВО ВІДКЛЮЧЕНО: @react-native-google-signin/google-signin потребує development build
// import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin'
import { supabase, getCurrentUserProfile } from '../supabase'
import * as AuthSession from 'expo-auth-session'
import * as WebBrowser from 'expo-web-browser'
import * as Crypto from 'expo-crypto'

// Конфігурація Google Sign-In для різних платформ
const GOOGLE_CONFIG = {
  // iOS Client ID
  iosClientId: '443512468984-ost3g20nqh0v1ght668fvmjoi1auh2a7.apps.googleusercontent.com',
  // Android Client ID
  androidClientId: '443512468984-6u4cfa6v4j3b9e4ck4e6ktp8924hi0b3.apps.googleusercontent.com',
  // Web Client ID (для Expo AuthSession)
  webClientId: '443512468984-ulck6pfk038lr7aang9v9g0brdbu0pj6.apps.googleusercontent.com'
}

// Налаштування WebBrowser для AuthSession
WebBrowser.maybeCompleteAuthSession()

/**
 * Ручне створення профілю користувача (fallback для тригера)
 */
async function createUserProfileManually(user: any) {
  try {
    const { error } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email,
        name: user.user_metadata?.name || user.user_metadata?.full_name || 'Користувач',
        avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture,
        subscription_type: 'free'
      })

    if (error) {
      console.error('❌ Manual profile creation error:', error)
    } else {
      console.log('✅ Profile created manually')
    }
  } catch (error) {
    console.error('❌ Manual profile creation failed:', error)
  }
}

export class MobileAuthService {
  // Клас залишається для можливого майбутнього використання
  // нативної автентифікації або інших специфічних мобільних сервісів.
  // Поточна логіка Google Sign-In повністю обробляється в AuthProvider.

  /**
   * Перевірка поточного стану автентифікації
   */
  static async getCurrentUser() {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      return user
    } catch (error) {
      console.error('❌ Помилка отримання поточного користувача:', error)
      return null
    }
  }

  /**
   * Перевірка чи користувач увійшов через Google
   * ТИМЧАСОВО: Перевіряємо через Supabase
   */
  static async isSignedInWithGoogle() {
    try {
      const user = await this.getCurrentUser()
      return user !== null && user.app_metadata?.provider === 'google'
    } catch (error) {
      console.error('❌ Помилка перевірки стану Google Sign-In:', error)
      return false
    }
  }
}