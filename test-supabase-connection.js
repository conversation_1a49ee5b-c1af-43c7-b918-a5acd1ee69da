// Тест підключення до Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Помилка: Відсутні змінні середовища SUPABASE_URL або SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  console.log('🔄 Перевірка підключення до Supabase...');
  console.log(`📍 URL: ${supabaseUrl}`);
  
  try {
    // Тест 1: Перевірка автентифікації
    const { data: authData, error: authError } = await supabase.auth.getSession();
    if (authError) {
      console.log('⚠️  Auth помилка (це нормально якщо не авторизовані):', authError.message);
    } else {
      console.log('✅ Auth працює');
    }

    // Тест 2: Перевірка доступу до таблиць
    console.log('\n🔄 Перевірка доступу до таблиць...');
    
    // Спробуємо отримати кількість профілів
    const { count, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Помилка доступу до таблиці profiles:', countError.message);
    } else {
      console.log(`✅ Таблиця profiles доступна. Кількість записів: ${count || 0}`);
    }

    // Тест 3: Перевірка інших таблиць
    const tables = ['projects', 'yarns', 'calculations', 'row_counters'];
    
    for (const table of tables) {
      const { error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.error(`❌ Таблиця ${table}: ${error.message}`);
      } else {
        console.log(`✅ Таблиця ${table} доступна`);
      }
    }

    console.log('\n✅ Підключення до Supabase успішне!');
    console.log('📊 База даних готова до роботи');
    
  } catch (error) {
    console.error('❌ Критична помилка:', error);
  }
}

// Запускаємо тест
testConnection();