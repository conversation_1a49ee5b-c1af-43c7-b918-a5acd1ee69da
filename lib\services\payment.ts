import { supabase } from '../supabase'
import * as WebBrowser from 'expo-web-browser'
import * as Crypto from 'expo-crypto'
import { Alert } from 'react-native'

interface PaymentData {
  amount: number
  description: string
  userId: string
  userEmail: string
  userName?: string
}

interface WayforpayRequest {
  merchantAccount: string
  merchantDomainName: string
  orderReference: string
  orderDate: number
  amount: number
  currency: string
  productName: string[]
  productCount: number[]
  productPrice: number[]
  clientEmail?: string
  clientFirstName?: string
  merchantSignature?: string
}

export class PaymentService {
  private static readonly MERCHANT_ACCOUNT = 'test_merch_n1' // Test merchant
  private static readonly MERCHANT_DOMAIN = 'knittingapp.com'
  private static readonly SECRET_KEY = 'flk3409refn54t54t*FNJRET' // Test secret key
  private static readonly CURRENCY = 'UAH'
  private static readonly PAYMENT_URL = 'https://secure.wayforpay.com/pay'

  /**
   * Генерує підпис для запиту
   * Використовуємо HMAC-SHA1 для безпеки
   */
  private static async generateSignature(params: string[]): Promise<string> {
    const signString = params.join(';')
    
    // Генеруємо HMAC-SHA1 підпис
    const signature = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA1,
      signString + ';' + this.SECRET_KEY,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    )
    
    return signature
  }

  /**
   * Створює платіж
   */
  static async createPayment(data: PaymentData): Promise<void> {
    try {
      const orderReference = `order_${data.userId}_${Date.now()}`
      const orderDate = Math.floor(Date.now() / 1000)

      // Параметри для підпису
      const signatureParams = [
        this.MERCHANT_ACCOUNT,
        this.MERCHANT_DOMAIN,
        orderReference,
        orderDate.toString(),
        data.amount.toString(),
        this.CURRENCY,
        data.description,
        '1',
        data.amount.toString()
      ]

      // Генеруємо підпис
      const signature = await this.generateSignature(signatureParams)

      // Формуємо запит
      const paymentRequest: WayforpayRequest = {
        merchantAccount: this.MERCHANT_ACCOUNT,
        merchantDomainName: this.MERCHANT_DOMAIN,
        orderReference,
        orderDate,
        amount: data.amount,
        currency: this.CURRENCY,
        productName: [data.description],
        productCount: [1],
        productPrice: [data.amount],
        clientEmail: data.userEmail,
        clientFirstName: data.userName,
        merchantSignature: signature
      }

      // Створюємо форму для відправки
      const form = new FormData()
      Object.entries(paymentRequest).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((item, index) => {
            form.append(`${key}[${index}]`, item.toString())
          })
        } else {
          form.append(key, value.toString())
        }
      })

      // Створюємо URL для платіжної форми
      const paymentUrl = `${this.PAYMENT_URL}?${new URLSearchParams(
        Object.entries(paymentRequest).reduce((acc, [key, value]) => {
          if (Array.isArray(value)) {
            value.forEach((item, index) => {
              acc[`${key}[${index}]`] = item.toString()
            })
          } else {
            acc[key] = value.toString()
          }
          return acc
        }, {} as Record<string, string>)
      ).toString()}`

      // Відкриваємо платіжну сторінку
      const result = await WebBrowser.openBrowserAsync(paymentUrl)
      
      // WebBrowser.openBrowserAsync повертає об'єкт з type: 'cancel' | 'dismiss'
      console.log('Payment page result:', result.type)
      
      // Показуємо повідомлення користувачу
      if (result.type === 'cancel' || result.type === 'dismiss') {
        Alert.alert(
          'Платіж',
          'Платіжну сторінку було закрито. Перевірте статус платежу пізніше.',
          [{ text: 'OK' }]
        )
      }

    } catch (error) {
      console.error('Payment error:', error)
      Alert.alert('Помилка', 'Не вдалося створити платіж')
      throw error
    }
  }

  /**
   * Перевіряє статус платежу
   */
  static async checkPaymentStatus(orderReference: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('payment_logs')
        .select('status')
        .eq('order_reference', orderReference)
        .single()

      if (error) throw error

      return data?.status === 'success'
    } catch (error) {
      console.error('Check payment status error:', error)
      return false
    }
  }

  /**
   * Отримує інформацію про підписку користувача
   */
  static async getSubscriptionStatus(userId: string): Promise<{
    isActive: boolean
    type: string
    expiresAt?: string
  }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('subscription_type, subscription_expires_at')
        .eq('id', userId)
        .single()

      if (error) throw error

      const isActive = data?.subscription_type === 'premium' && 
                      (!data.subscription_expires_at || new Date(data.subscription_expires_at) > new Date())

      return {
        isActive,
        type: data?.subscription_type || 'free',
        expiresAt: data?.subscription_expires_at
      }
    } catch (error) {
      console.error('Get subscription status error:', error)
      return { isActive: false, type: 'free' }
    }
  }

  /**
   * Тестові дані для оплати
   */
  static getTestCardData() {
    return {
      cardNumber: '****************',
      expiryMonth: '01',
      expiryYear: '2025',
      cvv: '123',
      cardHolder: 'TEST CARD'
    }
  }
}

// Експортуємо хук для використання в компонентах
export const usePayment = () => {
  const createPayment = async (amount: number, description: string) => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('name')
      .eq('id', user.id)
      .single()

    return PaymentService.createPayment({
      amount,
      description,
      userId: user.id,
      userEmail: user.email!,
      userName: profile?.name
    })
  }

  const checkSubscription = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { isActive: false, type: 'free' }
    }

    return PaymentService.getSubscriptionStatus(user.id)
  }

  return {
    createPayment,
    checkSubscription,
    testCardData: PaymentService.getTestCardData()
  }
}