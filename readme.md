# "Розрахуй і В'яжи" - Мобільний додаток для майстрів в'язання

![social-preview-dark](https://github.com/user-attachments/assets/9697a7da-10aa-4661-bb76-b5bc0dd611f0)

## 🧶 Про проєкт

Мобільний додаток для майстрів в'язання з 29 спеціалізованими калькуляторами, управлінням проєктами та офлайн-режимом. Побудований на React Native (Expo) з Supabase бекендом та WatermelonDB для локального зберігання.

### ✨ Ключові функції
- 🧮 **29 калькуляторів пряжі** - точні розрахунки для всіх типів виробів
- 📱 **Офлайн-режим** - робота без інтернету через WatermelonDB
- 📊 **Управління проєктами** - відстеження прогресу в'язання
- 🧵 **Каталог пряжі** - CRM система для запасів
- 👥 **Спільнота** - обмін досвідом з іншими майстрами
- 📸 **Галерея ідей** - систематизація референсів

## 🚀 Швидкий старт

Для повного налаштування середовища розробки, будь ласка, зверніться до нашого головного керівництва:

**➡️ [`QUICK_START_GUIDE.md`](./QUICK_START_GUIDE.md)**

Це керівництво містить всю необхідну інформацію про встановлення залежностей, налаштування Supabase, запуск проєкту та вирішення поширених проблем.

## 📚 Додаткова документація

- **[Налагодження Docker](./docs/DOCKER_TROUBLESHOOTING.md):** Покрокові інструкції для вирішення проблем з локальним середовищем Docker.
- **[ASCII Прототипи](./docs/ascii-prototypes/README.md):** Колекція текстових прототипів для UI/UX дизайну.
- **[Банк Пам'яті](./.kilocode/rules/memory-bank/README.md):** Внутрішня технічна документація для розробників та AI асистентів.

## 🛠 Технічний стек

- **Frontend:** React Native (Expo), TypeScript, NativeWind
- **Backend & Database:** Supabase (PostgreSQL, Auth, Storage), WatermelonDB
- **Архітектура:** Offline-First, Serverless

## Contributing

Contributions to this starter project are highly encouraged and welcome! If you have any suggestions, bug reports, or feature requests, please feel free to create an issue or submit a pull request.

## License

This repository is licensed under the MIT License. For more details, please refer to the [LICENSE](LICENSE) file.
