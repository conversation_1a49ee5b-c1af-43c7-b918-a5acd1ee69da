/**
 * Скрипт для автоматичного виправлення проблем з автентифікацією
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const TARGET_EMAIL = '<EMAIL>';

console.log('🔧 АВТОМАТИЧНЕ ВИПРАВЛЕННЯ ПРОБЛЕМ АВТЕНТИФІКАЦІЇ');
console.log('=' .repeat(50));

async function confirmUserEmail(userId) {
    console.log('\n📧 Підтвердження email користувача...');
    
    try {
        const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
            userId,
            { email_confirm: true }
        );
        
        if (error) {
            console.log('❌ Помилка підтвердження email:', error.message);
            return false;
        }
        
        console.log('✅ Email успішно підтверджений');
        return true;
    } catch (error) {
        console.log('❌ Помилка:', error.message);
        return false;
    }
}

async function createUserProfile(user) {
    console.log('\n👤 Створення профілю користувача...');
    
    try {
        const { data, error } = await supabaseAdmin
            .from('profiles')
            .insert({
                id: user.id,
                email: user.email,
                name: user.user_metadata?.name || user.email.split('@')[0],
                subscription_type: 'free'
            });
            
        if (error) {
            console.log('❌ Помилка створення профілю:', error.message);
            return false;
        }
        
        console.log('✅ Профіль успішно створений');
        return true;
    } catch (error) {
        console.log('❌ Помилка:', error.message);
        return false;
    }
}

async function resetUserPassword(userId) {
    console.log('\n🔐 Скидання паролю користувача...');
    
    try {
        const { data, error } = await supabaseAdmin.auth.admin.generateLink({
            type: 'recovery',
            email: TARGET_EMAIL
        });
        
        if (error) {
            console.log('❌ Помилка генерації посилання:', error.message);
            return false;
        }
        
        console.log('✅ Посилання для скидання паролю згенеровано:');
        console.log(`🔗 ${data.properties.action_link}`);
        console.log('📧 Надішліть це посилання користувачеві');
        return true;
    } catch (error) {
        console.log('❌ Помилка:', error.message);
        return false;
    }
}

async function setTemporaryPassword(userId) {
    console.log('\n🔑 Встановлення тимчасового паролю...');
    
    const tempPassword = 'TempPass123!';
    
    try {
        const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
            userId,
            { password: tempPassword }
        );
        
        if (error) {
            console.log('❌ Помилка встановлення паролю:', error.message);
            return false;
        }
        
        console.log('✅ Тимчасовий пароль встановлений');
        console.log(`🔐 Пароль: ${tempPassword}`);
        console.log('⚠️ ВАЖЛИВО: Попросіть користувача змінити пароль після входу!');
        return true;
    } catch (error) {
        console.log('❌ Помилка:', error.message);
        return false;
    }
}

async function main() {
    try {
        console.log(`📧 Виправлення проблем для: ${TARGET_EMAIL}`);
        
        // Знаходимо користувача
        const { data: users, error } = await supabaseAdmin.auth.admin.listUsers();
        
        if (error) {
            console.log('❌ Помилка отримання користувачів:', error.message);
            return;
        }
        
        const user = users.users.find(u => u.email === TARGET_EMAIL);
        
        if (!user) {
            console.log('❌ Користувач не знайдений. Створіть користувача спочатку.');
            return;
        }
        
        console.log(`✅ Користувач знайдений: ${user.id}`);
        
        // Перевіряємо та виправляємо email підтвердження
        if (!user.email_confirmed_at) {
            console.log('⚠️ Email не підтверджений - виправляємо...');
            await confirmUserEmail(user.id);
        } else {
            console.log('✅ Email вже підтверджений');
        }
        
        // Перевіряємо профіль
        const { data: profile, error: profileError } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
            
        if (profileError && profileError.code === 'PGRST116') {
            console.log('⚠️ Профіль не знайдений - створюємо...');
            await createUserProfile(user);
        } else if (profileError) {
            console.log('❌ Помилка перевірки профілю:', profileError.message);
        } else {
            console.log('✅ Профіль вже існує');
        }
        
        // Пропонуємо варіанти для паролю
        console.log('\n🔐 ВАРІАНТИ ДЛЯ ПАРОЛЮ:');
        console.log('1. Згенерувати посилання для скидання паролю');
        console.log('2. Встановити тимчасовий пароль');
        
        // Генеруємо посилання для скидання
        await resetUserPassword(user.id);
        
        // Встановлюємо тимчасовий пароль
        await setTemporaryPassword(user.id);
        
        console.log('\n' + '='.repeat(50));
        console.log('✅ ВИПРАВЛЕННЯ ЗАВЕРШЕНО');
        console.log('\n📋 НАСТУПНІ КРОКИ:');
        console.log('1. Спробуйте увійти з тимчасовим паролем: TempPass123!');
        console.log('2. Або використайте посилання для скидання паролю');
        console.log('3. Запустіть діагностику знову: node scripts/diagnose-auth-issue.js');
        
    } catch (error) {
        console.log('\n❌ КРИТИЧНА ПОМИЛКА:', error.message);
    }
}

main();