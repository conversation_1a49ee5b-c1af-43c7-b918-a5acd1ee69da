# ninja log v5
31	3250	7724926216712509	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	f72f6de7ce9e213f
2243	4914	7724926233539014	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9192dfeb14bab75dff9a3d59da63c491/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	faf5b1fc6a591ece
1	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
22	2235	7724926206586229	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	1823dc68e262231d
7969	8072	7724926264941370	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libappmodules.so	5fdeb7af5b88ce52
72	2761	7724926211883893	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	1452df09eac213c
12	7969	7724926263599188	CMakeFiles/appmodules.dir/C_/expo-supabase-starter/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	2dcb7e5c54c92a2e
2599	5584	7724926240218943	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	4edc9511a18d0c82
20	3035	7724926214684439	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	eb493dabc93a6df6
2236	4321	7724926227602743	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	1eb87eee65ac449e
15	3532	7724926219666887	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	9d72bfe01e801f19
60	2271	7724926206974282	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	48ba60b767e0a16b
122	4486	7724926229137962	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/338830e8d0445f324247eb51d07a4698/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	20ac1484aa00010b
109	2491	7724926209039041	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	fda623ec6b39dedb
81	2734	7724926211623092	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	36682bc5444c9d5f
76	3524	7724926219546539	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	9816c750cce987c7
133	3839	7724926222761243	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	e1ad082411de463d
17	2599	7724926210219825	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	8b34da2ec945446c
57	4641	7724926230611484	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	f163a8cb29161565
138	2965	7724926213909378	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	657a5f6355229a4d
113	3288	7724926216972398	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	15d439dd1090a530
3155	7246	7724926256660513	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	c5223d9a48154d22
85	3197	7724926216277552	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	7012f63e16dd6f14
90	3085	7724926215015544	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	e6a70ecc8170b067
25	3136	7724926215357828	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	68ada905d948f9b9
29	3261	7724926216724542	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	3b2e11a1734a2f7d
34	3433	7724926218603386	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	dc94dbd96e0bcf3c
94	3310	7724926217402976	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	7b2a9805fbd11ba9
3228	6488	7724926249163464	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	e5b62f85bc1086d1
2271	5169	7724926236073125	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afd6522afceba2d82bdea0dbcd01a58a/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	148f3bb3b1057580
99	3294	7724926217237779	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	aee5b847b5f30ff4
68	3444	7724926218795321	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	e96a777a058e7dad
104	3297	7724926217102679	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	c89e059f2dcba2de
117	3588	7724926220228885	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9fb98d8d4834f358bdc2633bd2f84fbe/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	48aff0e67a27ab60
2491	5531	7724926239643939	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	3ec50387015681b7
2761	5644	7724926240842414	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	ba48d8f14b00dfbd
62	4629	7723984028475848	CMakeFiles/appmodules.dir/OnLoad.cpp.o	c9c3be325e55acd0
63	4370	7724926227885126	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	67385f249a3abe5d
127	4677	7724926231009390	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/74560b550dadd0267fb5ff7265328ba1/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	605cf65b951ae451
5531	5609	7724926240423293	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libreact_codegen_safeareacontext.so	7d8ed51c43df690d
3483	6134	7723984043617169	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	e9f42dee8229d871
37	4334	7724926227634425	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	6dfc593c5266fa86
3086	4942	7724926233842156	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	59569a85aea9ca53
3692	6268	7723984044983408	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	643c68eb759998b6
2965	5323	7724926237634043	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/624b797ad5af4059517378102a78dbfe/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	bd9d0dfaf5d4db82
3756	6241	7723984044713134	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	b8118cd2bd814a52
3137	5897	7724926243366048	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/098e281487e1b1197bb33e4b04e378e7/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	8d2b87f98fa7e856
2735	5687	7724926241268282	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	a2d6e832a74c81c5
2769	5460	7724926239003427	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a118d84e46b64246
3036	5657	7724926240990700	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	49b1df08d10f9ed6
47	3155	7724926215660979	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	ad191f058bfa8f4a
40	2769	7724926211911136	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	9ab135f69900f86
53	2243	7724926206705541	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	25cea13ccc93b081
3197	5989	7724926244304182	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	95d409989a3c7086
3250	6311	7724926247418577	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	81b2707a3168bc2a
7246	7332	7724926257592162	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libreact_codegen_rnscreens.so	7d1f9be2b5ead02
43	3228	7724926216533729	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	27e2885f001191e5
50	3891	7724926223191607	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	b4e8da843f9d5e31
1	13	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
20	2308	7727172757690592	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	1823dc68e262231d
53	2374	7727172758309710	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	25cea13ccc93b081
112	2437	7727172759011181	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	fda623ec6b39dedb
62	2467	7727172759281880	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	48ba60b767e0a16b
17	2736	7727172762010547	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	8b34da2ec945446c
79	2773	7727172762288691	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	36682bc5444c9d5f
59	2790	7727172762404130	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	1452df09eac213c
37	2856	7727172763231716	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	9ab135f69900f86
130	3139	7727172765997937	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	657a5f6355229a4d
28	3158	7727172766084294	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	68ada905d948f9b9
13	3200	7727172766642615	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	eb493dabc93a6df6
70	3222	7727172766642615	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	7012f63e16dd6f14
44	3233	7727172766781392	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	ad191f058bfa8f4a
84	3305	7727172767429704	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	e6a70ecc8170b067
25	3360	7727172768107716	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	3b2e11a1734a2f7d
98	3376	7727172768192998	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	15d439dd1090a530
22	3401	7727172768589489	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	f72f6de7ce9e213f
47	3449	7727172768905197	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	27e2885f001191e5
88	3485	7727172769391443	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	7b2a9805fbd11ba9
34	3500	7727172769556950	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	dc94dbd96e0bcf3c
118	3509	7727172769586975	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	c89e059f2dcba2de
67	3511	7727172769693681	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	e96a777a058e7dad
93	3543	7727172770083989	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	aee5b847b5f30ff4
15	3600	7727172770668167	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	9d72bfe01e801f19
74	3692	7727172771546242	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	9816c750cce987c7
103	3915	7727172773815610	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9fb98d8d4834f358bdc2633bd2f84fbe/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	48aff0e67a27ab60
40	4063	7727172775248069	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	b4e8da843f9d5e31
10	4193	7727172776559663	CMakeFiles/appmodules.dir/OnLoad.cpp.o	c9c3be325e55acd0
31	4509	7727172779584274	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	6dfc593c5266fa86
56	4669	7727172781183098	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	67385f249a3abe5d
107	4785	7727172782485626	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/338830e8d0445f324247eb51d07a4698/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	20ac1484aa00010b
2374	4788	7727172782565797	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	1eb87eee65ac449e
50	4869	7727172783152020	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	f163a8cb29161565
124	5074	7727172785355098	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/74560b550dadd0267fb5ff7265328ba1/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	605cf65b951ae451
2309	5349	7727172788180939	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9192dfeb14bab75dff9a3d59da63c491/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	faf5b1fc6a591ece
3486	5459	7727172789351691	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	59569a85aea9ca53
2467	5622	7727172790908518	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afd6522afceba2d82bdea0dbcd01a58a/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	148f3bb3b1057580
3234	5936	7727172794083075	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	643c68eb759998b6
3402	5962	7727172794329848	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/624b797ad5af4059517378102a78dbfe/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	bd9d0dfaf5d4db82
3158	5990	7727172794615506	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	e9f42dee8229d871
2790	6010	7727172794835901	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	ba48d8f14b00dfbd
2737	6038	7727172795071513	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	e1ad082411de463d
2437	6040	7727172795081542	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	3ec50387015681b7
3360	6061	7727172795337477	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	b8118cd2bd814a52
6041	6136	7727172796030688	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libreact_codegen_safeareacontext.so	7d8ed51c43df690d
2857	6156	7727172796284160	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	4edc9511a18d0c82
2774	6309	7727172797773285	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	a2d6e832a74c81c5
3500	6380	7727172798561125	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	49b1df08d10f9ed6
3140	6384	7727172798591123	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a118d84e46b64246
3222	6451	7727172799258050	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	95d409989a3c7086
3376	6483	7727172799553506	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/098e281487e1b1197bb33e4b04e378e7/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	8d2b87f98fa7e856
3450	6842	7727172803085343	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	81b2707a3168bc2a
3200	7172	7727172806362670	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	e5b62f85bc1086d1
3306	7963	7727172814196885	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	c5223d9a48154d22
7963	8047	7727172815114804	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libreact_codegen_rnscreens.so	7d1f9be2b5ead02
7	8803	7727172822313642	CMakeFiles/appmodules.dir/C_/expo-supabase-starter/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	2dcb7e5c54c92a2e
8804	8909	7727172823667444	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/arm64-v8a/libappmodules.so	5fdeb7af5b88ce52
1	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
1	20	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
0	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
1	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
0	13	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
1	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
0	13	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
1	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
0	16	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/arm64-v8a/CMakeFiles/cmake.verify_globs	6761a19c72a7f41c
