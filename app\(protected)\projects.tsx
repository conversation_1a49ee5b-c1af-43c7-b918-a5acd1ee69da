import React, { useState } from 'react'
import { View, ScrollView, Text, Pressable, TextInput } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useColorScheme } from 'nativewind'
import { colors } from '../../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'

export default function ProjectsScreen() {
  const { colorScheme } = useColorScheme()
  const isDark = colorScheme === 'dark'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')

  const filters = [
    { id: 'all', label: 'Всі', count: 8 },
    { id: 'active', label: 'Активні', count: 3 },
    { id: 'completed', label: 'Завершені', count: 4 },
    { id: 'paused', label: 'На паузі', count: 1 }
  ]

  const projects = [
    {
      id: '1',
      title: 'Светр з косами',
      description: 'Теплий светр з красивим патерном кіс для зими',
      status: 'active',
      progress: 65,
      yarn: 'Пехорка Мериносовая, колір 02 (білий)',
      needles: 'Спиці 4.0 мм',
      startDate: '15 листопада 2024',
      lastWorked: '2 дні тому',
      image: null,
      notes: 'Потрібно перевірити довжину рукавів'
    },
    {
      id: '2',
      title: 'Дитяча шапочка',
      description: 'М\'яка шапочка для малюка з вушками',
      status: 'active',
      progress: 90,
      yarn: 'Baby Cotton, колір рожевий',
      needles: 'Спиці 3.5 мм',
      startDate: '1 грудня 2024',
      lastWorked: 'Вчора',
      image: null,
      notes: 'Залишилося тільки зшити'
    },
    {
      id: '3',
      title: 'Шкарпетки в смужку',
      description: 'Веселі шкарпетки з різнокольоровими смужками',
      status: 'completed',
      progress: 100,
      yarn: 'Regia 4-ply, мікс кольорів',
      needles: 'Чулочні спиці 2.5 мм',
      startDate: '20 жовтня 2024',
      lastWorked: '1 тиждень тому',
      image: null,
      notes: 'Дуже зручні, можна повторити'
    },
    {
      id: '4',
      title: 'Плед для дивану',
      description: 'Великий плед з квадратних мотивів',
      status: 'paused',
      progress: 25,
      yarn: 'Alize Lanagold, різні кольори',
      needles: 'Гачок 4.0 мм',
      startDate: '1 вересня 2024',
      lastWorked: '2 тижні тому',
      image: null,
      notes: 'Відкладено до весни'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return isDark ? colors.dark.primary : colors.light.primary
      case 'completed':
        return '#22c55e' // green
      case 'paused':
        return '#f59e0b' // amber
      default:
        return isDark ? colors.dark.mutedForeground : colors.light.mutedForeground
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Активний'
      case 'completed': return 'Завершений'
      case 'paused': return 'На паузі'
      default: return 'Невідомо'
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = selectedFilter === 'all' || project.status === selectedFilter
    return matchesSearch && matchesFilter
  })

  return (
    <SafeAreaView 
      style={{ 
        flex: 1, 
        backgroundColor: isDark ? colors.dark.background : colors.light.background 
      }}
    >
      <ScrollView className="flex-1 px-4">
        {/* Header */}
        <View className="py-6">
          <View className="flex-row justify-between items-center mb-4">
            <View>
              <Text 
                className="text-3xl font-bold mb-2"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                Мої проєкти 🧶
              </Text>
              <Text 
                className="text-base"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                Відстежуйте прогрес своїх робіт
              </Text>
            </View>
            <Pressable
              onPress={() => router.push('/projects/new')}
              className="w-12 h-12 rounded-full items-center justify-center"
              style={{ backgroundColor: isDark ? colors.dark.primary : colors.light.primary }}
            >
              <Ionicons 
                name="add" 
                size={24} 
                color={isDark ? colors.dark.primaryForeground : colors.light.primaryForeground}
              />
            </Pressable>
          </View>

          {/* Search */}
          <View 
            className="flex-row items-center px-4 py-3 rounded-xl mb-4"
            style={{ 
              backgroundColor: isDark ? colors.dark.card : colors.light.card,
              borderColor: isDark ? colors.dark.border : colors.light.border,
              borderWidth: 1
            }}
          >
            <Ionicons 
              name="search" 
              size={20} 
              color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
              style={{ marginRight: 12 }}
            />
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Пошук проєктів..."
              placeholderTextColor={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
              className="flex-1 text-base"
              style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
            />
          </View>

          {/* Filters */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-4">
            <View className="flex-row gap-2">
              {filters.map((filter) => (
                <Pressable
                  key={filter.id}
                  onPress={() => setSelectedFilter(filter.id)}
                  className="px-4 py-2 rounded-full flex-row items-center"
                  style={{ 
                    backgroundColor: selectedFilter === filter.id 
                      ? (isDark ? colors.dark.primary : colors.light.primary)
                      : (isDark ? colors.dark.card : colors.light.card),
                    borderColor: isDark ? colors.dark.border : colors.light.border,
                    borderWidth: selectedFilter === filter.id ? 0 : 1
                  }}
                >
                  <Text 
                    className="text-sm font-medium mr-1"
                    style={{ 
                      color: selectedFilter === filter.id 
                        ? (isDark ? colors.dark.primaryForeground : colors.light.primaryForeground)
                        : (isDark ? colors.dark.foreground : colors.light.foreground)
                    }}
                  >
                    {filter.label}
                  </Text>
                  <View 
                    className="w-5 h-5 rounded-full items-center justify-center"
                    style={{ 
                      backgroundColor: selectedFilter === filter.id 
                        ? (isDark ? colors.dark.primaryForeground : colors.light.primaryForeground)
                        : (isDark ? colors.dark.muted : colors.light.muted)
                    }}
                  >
                    <Text 
                      className="text-xs font-bold"
                      style={{ 
                        color: selectedFilter === filter.id 
                          ? (isDark ? colors.dark.primary : colors.light.primary)
                          : (isDark ? colors.dark.mutedForeground : colors.light.mutedForeground)
                      }}
                    >
                      {filter.count}
                    </Text>
                  </View>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Projects List */}
        <View className="mb-8">
          {filteredProjects.length > 0 ? (
            <View className="gap-4">
              {filteredProjects.map((project) => (
                <Pressable
                  key={project.id}
                  onPress={() => router.push(`/projects/${project.id}`)}
                  className="p-4 rounded-xl"
                  style={{ 
                    backgroundColor: isDark ? colors.dark.card : colors.light.card,
                    borderColor: isDark ? colors.dark.border : colors.light.border,
                    borderWidth: 1
                  }}
                >
                  {/* Project Header */}
                  <View className="flex-row justify-between items-start mb-3">
                    <View className="flex-1 mr-3">
                      <Text 
                        className="text-lg font-semibold mb-1"
                        style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                      >
                        {project.title}
                      </Text>
                      <Text 
                        className="text-sm"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {project.description}
                      </Text>
                    </View>
                    <View className="items-end">
                      <View 
                        className="px-3 py-1 rounded-full mb-2"
                        style={{ backgroundColor: getStatusColor(project.status) + '20' }}
                      >
                        <Text 
                          className="text-xs font-medium"
                          style={{ color: getStatusColor(project.status) }}
                        >
                          {getStatusLabel(project.status)}
                        </Text>
                      </View>
                      <Text 
                        className="text-sm font-medium"
                        style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                      >
                        {project.progress}%
                      </Text>
                    </View>
                  </View>

                  {/* Progress Bar */}
                  <View 
                    className="h-2 rounded-full mb-3"
                    style={{ backgroundColor: isDark ? colors.dark.muted : colors.light.muted }}
                  >
                    <View 
                      className="h-full rounded-full"
                      style={{ 
                        width: `${project.progress}%`,
                        backgroundColor: getStatusColor(project.status)
                      }}
                    />
                  </View>

                  {/* Project Details */}
                  <View className="gap-2">
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="library-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {project.yarn}
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="construct-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {project.needles}
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="time-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        Останній раз: {project.lastWorked}
                      </Text>
                    </View>
                  </View>

                  {/* Notes Preview */}
                  {project.notes && (
                    <View 
                      className="mt-3 p-3 rounded-lg"
                      style={{ backgroundColor: isDark ? colors.dark.muted : colors.light.muted }}
                    >
                      <Text 
                        className="text-sm"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        💭 {project.notes}
                      </Text>
                    </View>
                  )}
                </Pressable>
              ))}
            </View>
          ) : (
            <View 
              className="p-8 rounded-xl items-center"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Ionicons 
                name="folder-open-outline" 
                size={48} 
                color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                style={{ marginBottom: 12 }}
              />
              <Text 
                className="text-base font-medium mb-2"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                {searchQuery ? 'Проєкти не знайдено' : 'Поки що немає проєктів'}
              </Text>
              <Text 
                className="text-sm text-center mb-4"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                {searchQuery 
                  ? 'Спробуйте змінити пошуковий запит або фільтр'
                  : 'Створіть свій перший проєкт в\'язання'
                }
              </Text>
              {!searchQuery && (
                <Pressable
                  onPress={() => router.push('/projects/new')}
                  className="px-6 py-3 rounded-full"
                  style={{ backgroundColor: isDark ? colors.dark.primary : colors.light.primary }}
                >
                  <Text 
                    className="text-sm font-medium"
                    style={{ color: isDark ? colors.dark.primaryForeground : colors.light.primaryForeground }}
                  >
                    Створити проєкт
                  </Text>
                </Pressable>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}