const https = require('https');

// Конфігурація Supabase
const SUPABASE_URL = 'https://xaeztaeqyjubmpgjxcgh.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhhZXp0YWVxeWp1Ym1wZ2p4Y2doIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2NzI1OCwiZXhwIjoyMDY1NjQzMjU4fQ.D9ecOYjmUFGaEsC36gUduuOQlXAMIAx9Cuchi01or8M';

// Дані користувача
const USER_ID = 'f15eb141-46b2-4b87-b5f0-5542f60b27a5';
const USER_EMAIL = '<EMAIL>';

console.log('🔧 Мінімальний Profile Fix Script');
console.log('==================================');

async function getTableStructure() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: '/rest/v1/profiles?limit=0',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY,
        'Prefer': 'count=exact'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Статус відповіді: ${res.statusCode}`);
        console.log(`📋 Заголовки Content-Range:`, res.headers['content-range']);
        
        if (res.statusCode === 200) {
          console.log('✅ Таблиця profiles доступна');
          resolve(true);
        } else {
          console.log('❌ Помилка доступу до таблиці');
          console.log('📄 Відповідь:', data);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    console.log('🔍 Перевіряємо структуру таблиці profiles...');
    req.end();
  });
}

async function createMinimalProfile() {
  return new Promise((resolve, reject) => {
    // Створюємо профіль тільки з обов'язковими полями
    const profileData = {
      id: USER_ID,
      email: USER_EMAIL
    };

    const postData = JSON.stringify(profileData);
    
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: '/rest/v1/profiles',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY,
        'Prefer': 'return=representation'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Статус відповіді: ${res.statusCode}`);
        
        if (res.statusCode === 201 || res.statusCode === 200) {
          console.log('✅ Мінімальний профіль створено!');
          console.log('📄 Дані профілю:', JSON.parse(data));
          resolve(JSON.parse(data));
        } else {
          console.log('❌ Помилка створення профілю');
          console.log('📄 Відповідь сервера:', data);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    console.log('📤 Створюємо мінімальний профіль (тільки id + email)');
    console.log('📋 Дані:', profileData);
    
    req.write(postData);
    req.end();
  });
}

async function updateProfileWithName() {
  return new Promise((resolve, reject) => {
    const updateData = {
      name: 'Public Relations'
    };

    const postData = JSON.stringify(updateData);
    
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: `/rest/v1/profiles?id=eq.${USER_ID}`,
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY,
        'Prefer': 'return=representation'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Статус оновлення: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          console.log('✅ Ім\'я додано до профілю!');
          console.log('📄 Оновлені дані:', JSON.parse(data));
          resolve(JSON.parse(data));
        } else {
          console.log('⚠️  Не вдалося додати ім\'я (можливо, колонка відсутня)');
          console.log('📄 Відповідь:', data);
          // Не викидаємо помилку, оскільки основний профіль створено
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      console.log('⚠️  Помилка оновлення імені:', error.message);
      resolve(null);
    });

    console.log('📝 Спробуємо додати ім\'я до профілю...');
    
    req.write(postData);
    req.end();
  });
}

async function verifyProfile() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: `/rest/v1/profiles?id=eq.${USER_ID}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          const profiles = JSON.parse(data);
          if (profiles.length > 0) {
            console.log('✅ Профіль знайдено!');
            console.log('📄 Дані профілю:', profiles[0]);
            resolve(profiles[0]);
          } else {
            reject(new Error('Profile not found'));
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    console.log('🔍 Фінальна перевірка профілю...');
    req.end();
  });
}

async function main() {
  try {
    console.log(`👤 Користувач: ${USER_EMAIL} (ID: ${USER_ID})`);
    console.log('');

    // Перевіряємо доступ до таблиці
    await getTableStructure();
    console.log('');

    // Спочатку перевіримо, чи існує профіль
    try {
      await verifyProfile();
      console.log('ℹ️  Профіль вже існує');
      return;
    } catch (error) {
      console.log('ℹ️  Профіль не існує, створюємо...');
    }

    // Створюємо мінімальний профіль
    await createMinimalProfile();
    console.log('');

    // Спробуємо додати ім'я (може не спрацювати через schema cache)
    await updateProfileWithName();
    console.log('');

    // Фінальна перевірка
    await verifyProfile();
    
    console.log('');
    console.log('🎉 УСПІХ! Профіль створено');
    console.log('');
    console.log('📋 Тестуйте автентифікацію:');
    console.log(`   Email: ${USER_EMAIL}`);
    console.log('   Password: TempPass123!');
    
  } catch (error) {
    console.error('');
    console.error('💥 ПОМИЛКА:', error.message);
    console.error('');
    console.error('🔧 Можливі причини:');
    console.error('1. RLS політики блокують створення');
    console.error('2. Таблиця profiles не існує');
    console.error('3. Service Role Key недійсний');
    process.exit(1);
  }
}

main();