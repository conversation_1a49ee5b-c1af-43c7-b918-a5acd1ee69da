# Комплексне Керівництво з Діагностики та Вирішення Проблем з Android Емулятором

Це керівництво об'єднує найкращі практики та перевірені рішення з документації вашого проєкту для боротьби із зависаннями та проблемами продуктивності Android емулятора.

---

### 1. Повне та глибоке очищення кешів

Найчастіше проблеми викликані накопиченням застарілих даних. Повне очищення — це перший і найефективніший крок.

**Дія:** Запустіть автоматизований скрипт для комплексного очищення. Він зупинить усі пов'язані процеси (Node, Java), перезапустить ADB, очистить кеші Metro, Expo, Yarn та видалить артефакти збірки Android.

**Команда:**
```bash
# Запустити скрипт повного очищення
scripts\full-cleanup.bat
```
*   **Чому це важливо?** Цей скрипт, взятий з [`ANDROID_EMULATOR_CACHE_CLEANUP_GUIDE.md`](./ANDROID_EMULATOR_CACHE_CLEANUP_GUIDE.md), виконує 8 кроків очищення, що усуває 90% проблем, пов'язаних з кешем, і гарантує, що ви починаєте з чистого аркуша.

---

### 2. Діагностика та перезапуск Metro Bundler

Якщо очищення не допомогло, проблема може бути в Metro Bundler.

**Дія:** Перевірте, чи не зайнятий порт Metro, і запустіть його з очищенням кешу.

**Команди:**
```bash
# 1. Перевірити, чи не зайнятий порт 8081 іншим процесом
netstat -ano | findstr :8081

# Якщо порт зайнятий, завершіть процес за його PID
# taskkill /F /PID <PID_номер>

# 2. Запустити Metro з повним очищенням кешу
npx expo start --clear --reset-cache
```
*   **Чому це важливо?** Конфлікти портів — часта причина, через яку Metro не може запуститися. Прапор `--reset-cache` гарантує, що бандлер не буде використовувати пошкоджені або застарілі кешовані дані, як описано в [`ANDROID_EMULATOR_DIAGNOSTIC_PLAN.md`](./ANDROID_EMULATOR_DIAGNOSTIC_PLAN.md).

---

### 3. Оптимізація Android Emulator (AVD)

Неправильна конфігурація віртуального пристрою (AVD) безпосередньо впливає на його продуктивність.

**Дія:** Виконайте "холодний запуск" (Cold Boot) емулятора. Це повністю перезавантажує віртуальну машину, ігноруючи збережені стани (snapshots), які можуть бути пошкоджені.

**Команда:**
```bash
# Замініть Pixel_6 на ім'я вашого AVD
emulator -avd Pixel_6 -no-snapshot-load
```
*   **Чому це важливо?** Як зазначено в [`ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md`](./ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md), саме ця команда була ключовою для вирішення попередньої критичної проблеми. Вона гарантує чистий запуск емулятора.

**Додаткові поради щодо оптимізації AVD (в Android Studio):**
*   **Wipe Data:** В AVD Manager виберіть "Wipe Data" для вашого емулятора. Це видалить всі дані користувача і поверне емулятор до заводських налаштувань.
*   **Збільште RAM:** У налаштуваннях AVD збільште обсяг оперативної пам'яті (RAM) до 4 ГБ або вище.
*   **Оновіть образ системи:** Переконайтеся, що ви використовуєте останню версію образу системи для вашого AVD.

---

### 4. Перевірка конфігурації проєкту та залежностей

Проблеми можуть ховатися в пошкоджених `node_modules` або несумісних версіях бібліотек.

**Дія:** Повністю перевстановіть усі залежності проєкту.

**Команди:**
```bash
# 1. Видалити папку node_modules
rmdir /s /q node_modules

# 2. Очистити кеш yarn
yarn cache clean

# 3. Встановити залежності заново
yarn install
```
*   **Чому це важливо?** Ця процедура гарантує, що у вас встановлені саме ті версії залежностей, які вказані в `yarn.lock`, і виключає проблеми, пов'язані з пошкодженими або неконсистентними файлами в `node_modules`.

---

### 5. Аналіз системних ресурсів та оточення

Емулятор вимогливий до ресурсів. Зовнішні фактори можуть сильно впливати на його роботу.

**Дія:** Перевірте використання ЦП та ОЗП і виявіть ресурсомісткі процеси.

**Команди:**
```bash
# 1. Показати всі запущені процеси
tasklist

# 2. Відфільтрувати процеси Java (часто Gradle) та Node
tasklist | findstr java.exe
tasklist | findstr node.exe
```
*   **Чому це важливо?** Як показав [`ANDROID_EMULATOR_DIAGNOSTIC_REPORT.md`](./ANDROID_EMULATOR_DIAGNOSTIC_REPORT.md), процес Java (Gradle) може споживати більше 1 ГБ ОЗП. Якщо у вас запущено кілька IDE, Docker або антивірус активно сканує файли, це може призвести до "голодування" емулятора і його зависання.

**Рішення:**
*   **Закрийте зайві програми:** Завершіть роботу інших IDE, Docker Desktop, віртуальних машин.
*   **Налаштуйте антивірус:** Додайте папку проєкту та директорію Android SDK у виключення вашого антивірусу.
---

### 6. Примусове очищення середовища (PowerShell)

Якщо попередні кроки не допомогли і середовище розробки повністю "зависло", можна використати PowerShell-скрипт для примусового завершення всіх пов'язаних процесів.

**Дія:** Створіть та запустіть скрипт `cleanup.ps1` для повної зупинки емулятора, Metro та пов'язаних процесів.

**Код для скрипта (`scripts/cleanup.ps1`):**
```powershell
# cleanup.ps1
Write-Host "Stopping Metro bundler..."
taskkill /f /im node.exe 2>$null

Write-Host "Stopping Expo processes..."
taskkill /f /im expo.exe 2>$null

Write-Host "Stopping Android emulator..."
taskkill /f /im qemu-system-x86_64.exe 2>$null
taskkill /f /im emulator.exe 2>$null

Write-Host "Clearing ports..."
$processes = netstat -aon | findstr ":8081"
if ($processes) {
    $processes | ForEach-Object {
        $pid = ($_ -split '\s+')[-1]
        taskkill /PID $pid /F 2>$null
    }
}

Write-Host "Cleanup completed!"
```

**Запуск скрипта:**
```powershell
# Перейдіть до папки scripts і запустіть
.\scripts\cleanup.ps1
```

#### Рекомендації для стабільної роботи

*   **Правильне завершення:** Завжди використовуйте `Ctrl+C` у терміналі для зупинки Metro Bundler замість простого закриття вікна.
*   **Очищення кешу перед запуском:**
    ```powershell
    # Для Expo
    npx expo start --clear

    # Або для React Native CLI
    npx react-native start --reset-cache
    ```
*   **Оптимізація емулятора:** Якщо емулятор часто зависає, спробуйте:
    *   Збільшити обсяг RAM для емулятора в AVD Manager.
    *   Переконатися, що увімкнено Hardware Acceleration (апаратне прискорення) в налаштуваннях AVD.
    *   Додати папку Android SDK у виключення вашого антивірусу.

#### Створення PowerShell-аліасу для швидкого очищення

Для зручності ви можете створити аліас (псевдонім) `cleandev`.

1.  Відкрийте ваш профіль PowerShell командою: `notepad $PROFILE`
2.  Додайте в кінець файлу наступний код:
    ```powershell
    function Clear-DevEnvironment {
        Write-Host "Force stopping Node, Expo, and Emulator processes..."
        taskkill /f /im node.exe 2>$null
        taskkill /f /im expo.exe 2>$null
        taskkill /f /im qemu-system-x86_64.exe 2>$null
        taskkill /f /im emulator.exe 2>$null
        Write-Host "Development environment cleared!"
    }
    Set-Alias -Name "cleandev" -Value Clear-DevEnvironment
    ```
3.  Збережіть файл та перезапустіть термінал. Тепер ви можете просто виконати команду `cleandev` для швидкого очищення.