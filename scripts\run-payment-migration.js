const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function runPaymentMigration() {
  try {
    console.log('🚀 Виконання платіжної міграції...\n');
    
    // Читаємо файл міграції
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '003_add_payment_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Розділяємо на окремі команди
    const commands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    console.log(`📄 Знайдено ${commands.length} SQL команд для виконання\n`);
    
    // Виконуємо кожну команду окремо
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      
      if (command.toLowerCase().includes('alter table profiles add column')) {
        console.log(`⚡ Виконання команди ${i + 1}: Додавання поля subscription_expires_at`);
        
        try {
          const { error } = await supabase.rpc('exec_sql', { 
            sql: command + ';' 
          });
          
          if (error) {
            if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
              console.log('   ℹ️  Поле вже існує, пропускаємо');
            } else {
              console.log('   ❌ Помилка:', error.message);
            }
          } else {
            console.log('   ✅ Успішно виконано');
          }
        } catch (err) {
          console.log('   ⚠️  Альтернативний метод...');
          // Спробуємо через прямий SQL
          try {
            await supabase.from('profiles').select('subscription_expires_at').limit(1);
            console.log('   ℹ️  Поле вже існує');
          } catch {
            console.log('   ❌ Поле потрібно додати вручну');
          }
        }
      }
      
      else if (command.toLowerCase().includes('create table payment_logs')) {
        console.log(`⚡ Виконання команди ${i + 1}: Створення таблиці payment_logs`);
        
        try {
          const { error } = await supabase.rpc('exec_sql', { 
            sql: command + ';' 
          });
          
          if (error) {
            if (error.message.includes('already exists')) {
              console.log('   ℹ️  Таблиця вже існує, пропускаємо');
            } else {
              console.log('   ❌ Помилка:', error.message);
            }
          } else {
            console.log('   ✅ Успішно створено');
          }
        } catch (err) {
          console.log('   ⚠️  Перевіряємо існування таблиці...');
          try {
            await supabase.from('payment_logs').select('*').limit(1);
            console.log('   ℹ️  Таблиця вже існує');
          } catch {
            console.log('   ❌ Таблицю потрібно створити вручну');
          }
        }
      }
      
      else if (command.toLowerCase().includes('create table subscriptions')) {
        console.log(`⚡ Виконання команди ${i + 1}: Створення таблиці subscriptions`);
        
        try {
          const { error } = await supabase.rpc('exec_sql', { 
            sql: command + ';' 
          });
          
          if (error) {
            if (error.message.includes('already exists')) {
              console.log('   ℹ️  Таблиця вже існує, пропускаємо');
            } else {
              console.log('   ❌ Помилка:', error.message);
            }
          } else {
            console.log('   ✅ Успішно створено');
          }
        } catch (err) {
          console.log('   ⚠️  Перевіряємо існування таблиці...');
          try {
            await supabase.from('subscriptions').select('*').limit(1);
            console.log('   ℹ️  Таблиця вже існує');
          } catch {
            console.log('   ❌ Таблицю потрібно створити вручну');
          }
        }
      }
      
      else if (command.toLowerCase().includes('alter table') && command.toLowerCase().includes('enable row level security')) {
        console.log(`⚡ Виконання команди ${i + 1}: Увімкнення RLS`);
        console.log('   ⚠️  RLS потрібно налаштувати через Supabase Dashboard');
      }
      
      else if (command.toLowerCase().includes('create policy')) {
        console.log(`⚡ Виконання команди ${i + 1}: Створення RLS політики`);
        console.log('   ⚠️  RLS політики потрібно створити через Supabase Dashboard');
      }
      
      else {
        console.log(`⚡ Виконання команди ${i + 1}: ${command.substring(0, 50)}...`);
        console.log('   ⚠️  Команду потрібно виконати через SQL Editor');
      }
    }
    
    console.log('\n🔍 Перевірка результатів...\n');
    
    // Перевіряємо створені таблиці та поля
    const checks = [
      { table: 'payment_logs', description: 'Таблиця логів платежів' },
      { table: 'subscriptions', description: 'Таблиця підписок' }
    ];
    
    for (const check of checks) {
      try {
        const { data, error } = await supabase
          .from(check.table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${check.description}: ${error.message}`);
        } else {
          console.log(`✅ ${check.description}: створено та доступна`);
        }
      } catch (err) {
        console.log(`❌ ${check.description}: помилка доступу`);
      }
    }
    
    // Перевіряємо поле subscription_expires_at
    try {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'profiles')
        .eq('column_name', 'subscription_expires_at');
      
      if (data && data.length > 0) {
        console.log('✅ Поле subscription_expires_at: додано до таблиці profiles');
      } else {
        console.log('❌ Поле subscription_expires_at: відсутнє в таблиці profiles');
      }
    } catch (err) {
      console.log('❌ Поле subscription_expires_at: помилка перевірки');
    }
    
    console.log('\n✨ Платіжна міграція завершена!');
    
  } catch (error) {
    console.error('❌ Помилка виконання міграції:', error.message);
  }
}

runPaymentMigration();