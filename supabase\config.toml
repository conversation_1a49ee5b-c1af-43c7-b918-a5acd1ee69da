# supabase/config.toml

# Ця секція контролює налаштування сервера автентифікації (GoTrue)
[auth.email]
# Дозволяє або забороняє реєстрацію нових користувачів через пошту.
# Залиште true, щоб реєстрація працювала.
enable_signup = true

# Встановлення цього параметра в 'false' ВИМИКАЄ необхідність
# підтвердження пошти. Користувач буде активований одразу.
# Це саме те, що вам потрібно для локальної розробки.
enable_confirmations = false