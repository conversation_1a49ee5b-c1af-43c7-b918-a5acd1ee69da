# План Виправлення Потоку Навігації

**Дата:** 27.06.2025

## 1. Проблема

Кнопка "ПРИДБАТИ ЗАРАЗ" на екрані перегляду скріншотів (`app/welcome.tsx`) веде користувача за неправильним маршрутом (`/(protected)/(tabs)`), замість того, щоб направити його на екран вибору планів підписки (`/subscription-plans`). Це створює логічну помилку в потоці залучення нових користувачів.

## 2. План Виправлення

### Мета
Змінити обробник `onPress` для кнопки "ПРИДБАТИ ЗАРАЗ" на екрані перегляду скріншотів, щоб він перенаправляв користувача на екран `/subscription-plans`.

### Файл для зміни
- `app/welcome.tsx`

### Зміни в коді
- **Знайти:** Блок коду, що відповідає за кнопку "ПРИДБАТИ ЗАРАЗ" (орієнтовно рядки 198-208).
- **Змінити:** Виклик `router.push("/(protected)/(tabs)")` на `router.push("/subscription-plans")`.

## 3. Візуалізація Потоку (Mermaid)

### Поточний (неправильний) потік:
```mermaid
graph TD
    A[Welcome Screen] -->|Натискає "Переглянути скріншоти"| B(Screenshot Viewer);
    B -->|Натискає "Придбати зараз"| C{/(protected)/(tabs)};
    C -->|Користувач не автентифікований| D[Redirect на Welcome/Login];
```

### Бажаний (правильний) потік:
```mermaid
graph TD
    A[Welcome Screen] -->|Натискає "Переглянути скріншоти"| B(Screenshot Viewer);
    B -->|Натискає "Придбати зараз"| E[/subscription-plans];
    A -->|Натискає "Придбати підписку"| E[/subscription-plans];
    E --> F[Вибір плану і реєстрація];
```

## 4. Наступний Крок

Після документування цього плану, наступним кроком є перехід в режим `code` для безпосереднього внесення змін до файлу `app/welcome.tsx`.