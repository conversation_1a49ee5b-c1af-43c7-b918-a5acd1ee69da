export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          name: string | null
          avatar_url: string | null
          subscription_type: string
          subscription_expires_at: string | null
          preferences: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name?: string | null
          avatar_url?: string | null
          subscription_type?: string
          subscription_expires_at?: string | null
          preferences?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          avatar_url?: string | null
          subscription_type?: string
          subscription_expires_at?: string | null
          preferences?: Json
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          title: string
          description: string | null
          status: string
          pattern_data: Json
          images: string[]
          notes: string | null
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          status?: string
          pattern_data?: Json
          images?: string[]
          notes?: string | null
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          status?: string
          pattern_data?: Json
          images?: string[]
          notes?: string | null
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      yarns: {
        Row: {
          id: string
          brand: string
          name: string
          color: string
          weight_category: string
          fiber_content: Json
          yardage_per_skein: number
          weight_per_skein: number
          quantity_available: number
          cost_per_skein: number | null
          images: string[]
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          brand: string
          name: string
          color: string
          weight_category: string
          fiber_content?: Json
          yardage_per_skein: number
          weight_per_skein: number
          quantity_available?: number
          cost_per_skein?: number | null
          images?: string[]
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          brand?: string
          name?: string
          color?: string
          weight_category?: string
          fiber_content?: Json
          yardage_per_skein?: number
          weight_per_skein?: number
          quantity_available?: number
          cost_per_skein?: number | null
          images?: string[]
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      calculations: {
        Row: {
          id: string
          type: string
          input_data: Json
          result_data: Json
          project_id: string | null
          user_id: string
          created_at: string
        }
        Insert: {
          id?: string
          type: string
          input_data: Json
          result_data: Json
          project_id?: string | null
          user_id: string
          created_at?: string
        }
        Update: {
          id?: string
          type?: string
          input_data?: Json
          result_data?: Json
          project_id?: string | null
          user_id?: string
          created_at?: string
        }
      }
      row_counters: {
        Row: {
          id: string
          name: string
          current_count: number
          target_count: number | null
          increment: number
          project_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          current_count?: number
          target_count?: number | null
          increment?: number
          project_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          current_count?: number
          target_count?: number | null
          increment?: number
          project_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      inspiration_gallery: {
        Row: {
          id: string
          title: string
          description: string | null
          image_url: string
          source_url: string | null
          tags: string[]
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          image_url: string
          source_url?: string | null
          tags?: string[]
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          image_url?: string
          source_url?: string | null
          tags?: string[]
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      community_posts: {
        Row: {
          id: string
          title: string
          content: string
          images: string[]
          is_public: boolean
          project_id: string | null
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          images?: string[]
          is_public?: boolean
          project_id?: string | null
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          images?: string[]
          is_public?: boolean
          project_id?: string | null
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          content: string
          post_id: string
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          content: string
          post_id: string
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          content?: string
          post_id?: string
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      likes: {
        Row: {
          id: string
          post_id: string
          user_id: string
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          user_id: string
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          user_id?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}