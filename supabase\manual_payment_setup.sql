-- Мін<PERSON><PERSON>альний SQL для виправлення платіжної системи
-- Виконати через Supabase Dashboard → SQL Editor

-- 1. Додаємо поле subscription_expires_at до таблиці profiles
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_expires_at TIMESTAMP WITH TIME ZONE;

-- 2. Створюємо таблицю payment_logs
CREATE TABLE IF NOT EXISTS payment_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  order_reference TEXT NOT NULL,
  amount NUMERIC NOT NULL,
  currency TEXT DEFAULT 'UAH',
  status TEXT NOT NULL, -- 'pending', 'success', 'failed', 'cancelled'
  payment_method TEXT DEFAULT 'wayforpay',
  wayforpay_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Створюємо таблицю subscriptions
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  plan_type TEXT NOT NULL, -- 'monthly', 'yearly'
  status TEXT NOT NULL, -- 'active', 'expired', 'cancelled'
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  payment_log_id UUID REFERENCES payment_logs(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Увімкнення RLS для нових таблиць
ALTER TABLE payment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- 5. Створення RLS політик для payment_logs
CREATE POLICY "Users can view own payment logs" ON payment_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment logs" ON payment_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 6. Створення RLS політик для subscriptions
CREATE POLICY "Users can view own subscriptions" ON subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscriptions" ON subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 7. Створення індексів для оптимізації
CREATE INDEX IF NOT EXISTS idx_payment_logs_user_id ON payment_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_order_reference ON payment_logs(order_reference);
CREATE INDEX IF NOT EXISTS idx_payment_logs_status ON payment_logs(status);

CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires_at ON subscriptions(expires_at);

-- 8. Функція для автоматичного оновлення updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. Тригери для автоматичного оновлення updated_at
CREATE TRIGGER update_payment_logs_updated_at 
  BEFORE UPDATE ON payment_logs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at 
  BEFORE UPDATE ON subscriptions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Функція для оновлення профілю при зміні підписки
CREATE OR REPLACE FUNCTION update_profile_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Оновлюємо subscription_expires_at в профілі
    UPDATE profiles 
    SET subscription_expires_at = NEW.expires_at
    WHERE id = NEW.user_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 11. Тригер для автоматичного оновлення профілю
CREATE TRIGGER update_profile_on_subscription_change
  AFTER INSERT OR UPDATE ON subscriptions
  FOR EACH ROW EXECUTE FUNCTION update_profile_on_subscription_change();