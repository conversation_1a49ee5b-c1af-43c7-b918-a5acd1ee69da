import React from 'react';
import { useAuth } from '@/context/supabase-provider';

export default function RootLayout() {
  const { initialized } = useAuth();

  // AuthProvider повністю керує навігацією через useEffect
  // Тут ми просто показуємо нічого, поки AuthProvider не завершить ініціалізацію
  if (!initialized) {
    return null;
  }

  // Після ініціалізації AuthProvider сам перенаправить користувача
  // на відповідний екран (welcome або calculators)
  return null;
}