@echo off
echo ========================================
echo ДІАГНОСТИКА ANDROID ЕМУЛЯТОРА
echo Проєкт: "Розрахуй і В'яжи"
echo Дата: %date% %time%
echo ========================================

echo.
echo [1/6] Перевірка емулятора...
echo ----------------------------------------
adb devices -l
if %errorlevel% neq 0 (
    echo ❌ ПОМИЛКА: Емулятор не підключений
    echo Рішення: Запустіть емулятор через Android Studio
    goto :end
) else (
    echo ✅ Емулятор підключений
)

echo.
echo [2/6] Перевірка портів...
echo ----------------------------------------
echo Перевірка порту Metro (8081):
netstat -ano | findstr :8081
if %errorlevel% equ 0 (
    echo ✅ Порт 8081 зайнятий (Metro працює)
) else (
    echo ⚠️ Порт 8081 вільний (Metro не запущений)
)

echo.
echo Перевірка портів Expo (19000, 19001):
netstat -ano | findstr :19000
netstat -ano | findstr :19001

echo.
echo [3/6] Перевірка процесів...
echo ----------------------------------------
echo Node.js процеси:
tasklist | findstr node.exe
if %errorlevel% neq 0 (
    echo ⚠️ Node.js процеси не знайдені
) else (
    echo ✅ Node.js процеси активні
)

echo.
echo Java процеси (Gradle):
tasklist | findstr java.exe
if %errorlevel% neq 0 (
    echo ⚠️ Java процеси не знайдені
) else (
    echo ✅ Java процеси активні
)

echo.
echo [4/6] Перевірка мережі емулятора...
echo ----------------------------------------
echo Тестування підключення до localhost (********):
adb shell ping -c 1 ********
if %errorlevel% equ 0 (
    echo ✅ Мережа емулятора працює
) else (
    echo ❌ Проблеми з мережею емулятора
)

echo.
echo [5/6] Перевірка додатку...
echo ----------------------------------------
echo Пошук встановленого додатку:
adb shell pm list packages | findstr knittingapp
if %errorlevel% equ 0 (
    echo ✅ Додаток встановлений
) else (
    echo ⚠️ Додаток не встановлений або не знайдений
)

echo.
echo [6/6] Перевірка логів (останні помилки)...
echo ----------------------------------------
echo Пошук критичних помилок:
adb logcat -t 50 | findstr -i "error\|exception\|crash\|fatal"
if %errorlevel% neq 0 (
    echo ✅ Критичних помилок не знайдено
) else (
    echo ❌ Знайдені помилки в логах
)

echo.
echo ========================================
echo РЕЗУЛЬТАТИ ДІАГНОСТИКИ
echo ========================================

echo.
echo Рекомендації на основі діагностики:
echo.

REM Перевірка основних проблем
adb devices -l | findstr device >nul
if %errorlevel% neq 0 (
    echo 🔧 1. КРИТИЧНО: Запустіть Android емулятор
    echo    - Відкрийте Android Studio
    echo    - AVD Manager → Start емулятор
    echo    - Або використайте Cold Boot
)

netstat -ano | findstr :8081 >nul
if %errorlevel% neq 0 (
    echo 🔧 2. Запустіть Metro сервер:
    echo    - npx expo start --clear --android
    echo    - або yarn start --clear
)

tasklist | findstr node.exe >nul
if %errorlevel% neq 0 (
    echo 🔧 3. Node.js процеси не активні
    echo    - Можливо потрібно запустити Metro
)

echo.
echo 📋 Наступні кроки:
echo 1. Якщо є проблеми - виконайте: scripts\full-cleanup.bat
echo 2. Перезапустіть емулятор з Cold Boot
echo 3. Запустіть: npx expo start --clear --android
echo 4. Моніторьте логи: adb logcat ^| findstr -i "expo\|react\|metro"

:end
echo.
echo ========================================
echo ДІАГНОСТИКА ЗАВЕРШЕНА
echo ========================================
pause