# Покрокова інструкція налаштування автентифікації

## ✅ Що вже реалізовано в коді:

1. **Екрани автентифікації:**
   - Welcome screen (app/welcome.tsx) - з кнопкою швидкого входу через біометрію
   - Sign In (app/sign-in.tsx) - вхід з email/password та соціальними мережами
   - Sign Up (app/sign-up.tsx) - реєстрація з вибором плану підписки
   - Forgot Password (app/forgot-password.tsx) - відновлення паролю
   - Subscription Plans (app/subscription-plans.tsx) - вибір тарифного плану
   - Payment (app/payment.tsx) - форма оплати

2. **Функціональність:**
   - Email/password автентифікація
   - Соціальна автентифікація (Google, Apple)
   - Біометрична автентифікація (Touch ID/Face ID)
   - Збереження сесії в SecureStore
   - Захищені маршрути

## 📋 Що потрібно зробити вручну:

### 1. Налаштування Supabase проєкту

1. **Створіть проєкт на Supabase:**
   - Перейдіть на https://supabase.com
   - Натисніть "Start your project"
   - Увійдіть через GitHub
   - Створіть новий проєкт з назвою "knitting-calculator"
   - Виберіть регіон (рекомендую Frankfurt для України)
   - Встановіть пароль бази даних (збережіть його!)

2. **Отримайте ключі API:**
   - Після створення проєкту перейдіть в Settings → API
   - Скопіюйте:
     - Project URL (виглядає як https://xxxxx.supabase.co)
     - anon public key (довгий рядок)

3. **Оновіть .env.local файл:**
   ```
   EXPO_PUBLIC_SUPABASE_URL=ваш_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=ваш_anon_key
   ```

### 2. Налаштування таблиць в Supabase

1. **Перейдіть в SQL Editor** в Supabase Dashboard

2. **Виконайте наступний SQL скрипт:**
   ```sql
   -- Створення таблиці profiles
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
     email TEXT UNIQUE NOT NULL,
     name TEXT,
     avatar_url TEXT,
     subscription_type TEXT DEFAULT 'free',
     subscription_expires_at TIMESTAMP WITH TIME ZONE,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- RLS політики для profiles
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

   CREATE POLICY "Users can view own profile" ON profiles
     FOR SELECT USING (auth.uid() = id);

   CREATE POLICY "Users can update own profile" ON profiles
     FOR UPDATE USING (auth.uid() = id);

   -- Функція для автоматичного створення профілю
   CREATE OR REPLACE FUNCTION public.handle_new_user()
   RETURNS trigger AS $$
   BEGIN
     INSERT INTO public.profiles (id, email, name)
     VALUES (new.id, new.email, new.raw_user_meta_data->>'name');
     RETURN new;
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;

   -- Тригер для нових користувачів
   CREATE TRIGGER on_auth_user_created
     AFTER INSERT ON auth.users
     FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
   ```

### 3. Налаштування Email Templates

1. **Перейдіть в Authentication → Email Templates**

2. **Оновіть шаблони українською мовою:**

   **Confirm signup (Підтвердження реєстрації):**
   ```html
   <h2>Підтвердіть вашу електронну адресу</h2>
   <p>Дякуємо за реєстрацію в додатку "Розрахуй і В'яжи"!</p>
   <p>Натисніть кнопку нижче, щоб підтвердити вашу електронну адресу:</p>
   <a href="{{ .ConfirmationURL }}">Підтвердити email</a>
   ```

   **Reset Password (Відновлення паролю):**
   ```html
   <h2>Відновлення паролю</h2>
   <p>Ви запросили відновлення паролю для додатку "Розрахуй і В'яжи".</p>
   <p>Натисніть кнопку нижче, щоб встановити новий пароль:</p>
   <a href="{{ .ConfirmationURL }}">Встановити новий пароль</a>
   <p>Якщо ви не запитували відновлення паролю, проігноруйте цей лист.</p>
   ```

### 4. Налаштування OAuth провайдерів

#### Google OAuth:

1. **Створіть проєкт в Google Cloud Console:**
   - Перейдіть на https://console.cloud.google.com
   - Створіть новий проєкт
   - Увімкніть Google+ API

2. **Створіть OAuth 2.0 credentials:**
   - APIs & Services → Credentials → Create Credentials → OAuth client ID
   - Application type: Web application
   - Authorized redirect URIs: 
     - `https://ваш-проєкт.supabase.co/auth/v1/callback`
     - `com.knittingapp.calculator://auth/callback`

3. **Додайте ключі в Supabase:**
   - Authentication → Providers → Google
   - Вставте Client ID та Client Secret

#### Apple Sign In:

1. **Налаштуйте в Apple Developer:**
   - Створіть App ID з Sign in with Apple capability
   - Створіть Service ID для веб-автентифікації
   - Створіть приватний ключ

2. **Додайте в Supabase:**
   - Authentication → Providers → Apple
   - Заповніть всі необхідні поля

### 5. Налаштування Deep Linking

1. **Для Android (app.json):**
   ```json
   {
     "expo": {
       "android": {
         "intentFilters": [
           {
             "action": "VIEW",
             "autoVerify": true,
             "data": [
               {
                 "scheme": "com.knittingapp.calculator",
                 "host": "auth",
                 "pathPrefix": "/callback"
               }
             ],
             "category": ["BROWSABLE", "DEFAULT"]
           }
         ]
       }
     }
   }
   ```

2. **Для iOS (app.json):**
   ```json
   {
     "expo": {
       "ios": {
         "associatedDomains": [
           "applinks:ваш-проєкт.supabase.co"
         ]
       }
     }
   }
   ```

### 6. Налаштування біометрії

**Додайте дозволи в app.json:**
```json
{
  "expo": {
    "plugins": [
      [
        "expo-local-authentication",
        {
          "faceIDPermission": "Дозвольте додатку використовувати Face ID для швидкого входу"
        }
      ]
    ]
  }
}
```

### 7. Тестування

1. **Перезапустіть додаток:**
   ```bash
   yarn start --clear
   ```

2. **Перевірте функціональність:**
   - [ ] Реєстрація нового користувача
   - [ ] Вхід з email/password
   - [ ] Відновлення паролю
   - [ ] Вхід через Google
   - [ ] Вхід через Apple (тільки на реальному iOS пристрої)
   - [ ] Біометричний вхід
   - [ ] Вибір та оплата підписки

### 8. Налаштування WayForPay (для платежів)

1. **Отримайте merchant account:**
   - Зареєструйтеся на https://wayforpay.com
   - Отримайте merchantAccount та secretKey

2. **Додайте в .env.local:**
   ```
   EXPO_PUBLIC_WAYFORPAY_MERCHANT=ваш_merchant_account
   WAYFORPAY_SECRET_KEY=ваш_secret_key
   ```

3. **Створіть Edge Function для обробки платежів:**
   ```bash
   supabase functions new payment-webhook
   ```

## 🚨 Важливі примітки:

1. **Безпека:**
   - Ніколи не комітьте .env.local файл
   - Використовуйте RLS політики для всіх таблиць
   - Зберігайте чутливі дані в Supabase Vault

2. **Продакшн:**
   - Увімкніть email підтвердження в Authentication → Settings
   - Налаштуйте власний SMTP для надсилання листів
   - Додайте rate limiting для API

3. **Моніторинг:**
   - Налаштуйте Sentry для відстеження помилок
   - Додайте аналітику для конверсії реєстрацій

## 📞 Підтримка:

Якщо виникнуть питання під час налаштування:
- Supabase Discord: https://discord.supabase.com
- Документація: https://supabase.com/docs
- Expo форум: https://forums.expo.dev

---

Після виконання всіх кроків ваша система автентифікації буде повністю готова до використання!