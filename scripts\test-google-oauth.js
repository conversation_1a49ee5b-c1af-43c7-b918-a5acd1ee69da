#!/usr/bin/env node

/**
 * Скрипт для тестування Google OAuth конфігурації
 * Перевіряє redirect URI та Google Cloud Console налаштування
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 Тестування Google OAuth конфігурації...\n')

// Функція для виконання команд
function runCommand(command, description) {
  try {
    console.log(`🔄 ${description}...`)
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' })
    console.log(`✅ ${description} - успішно`)
    return result
  } catch (error) {
    console.error(`❌ ${description} - помилка:`, error.message)
    return null
  }
}

// Функція для перевірки файлів
function checkFile(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${description} - існує`)
      return true
    } else {
      console.log(`❌ ${description} - не знайдено`)
      return false
    }
  } catch (error) {
    console.log(`❌ ${description} - помилка:`, error.message)
    return false
  }
}

// Функція для читання конфігурації
function readConfig(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    return JSON.parse(content)
  } catch (error) {
    console.error(`❌ Не вдалося прочитати ${filePath}:`, error.message)
    return null
  }
}

async function main() {
  console.log('📋 Перевірка файлів конфігурації:')
  
  // Перевірка основних файлів
  const files = [
    { path: 'app.json', desc: 'Expo конфігурація' },
    { path: 'lib/services/mobile-auth.ts', desc: 'Mobile Auth сервіс' },
    { path: 'lib/supabase.ts', desc: 'Supabase конфігурація' },
    { path: '.env.local', desc: 'Environment змінні' }
  ]
  
  files.forEach(file => {
    checkFile(file.path, file.desc)
  })
  
  console.log('\n📱 Перевірка Expo конфігурації:')
  
  // Читання app.json
  const appConfig = readConfig('app.json')
  if (appConfig && appConfig.expo) {
    const expo = appConfig.expo
    
    console.log(`✅ App Name: ${expo.name}`)
    console.log(`✅ Slug: ${expo.slug}`)
    console.log(`✅ Scheme: ${expo.scheme || 'не налаштовано'}`)
    
    if (expo.scheme === 'knittingapp') {
      console.log('✅ Custom scheme налаштовано правильно')
    } else {
      console.log('⚠️ Custom scheme може потребувати налаштування')
    }
  }
  
  console.log('\n🔑 Перевірка Google OAuth Client IDs:')
  
  // Читання mobile-auth.ts для перевірки Client IDs
  try {
    const authContent = fs.readFileSync('lib/services/mobile-auth.ts', 'utf8')
    
    const webClientMatch = authContent.match(/webClientId:\s*'([^']+)'/)
    const iosClientMatch = authContent.match(/iosClientId:\s*'([^']+)'/)
    const androidClientMatch = authContent.match(/androidClientId:\s*'([^']+)'/)
    
    if (webClientMatch) {
      console.log(`✅ Web Client ID: ${webClientMatch[1]}`)
    } else {
      console.log('❌ Web Client ID не знайдено')
    }
    
    if (iosClientMatch) {
      console.log(`✅ iOS Client ID: ${iosClientMatch[1]}`)
    } else {
      console.log('❌ iOS Client ID не знайдено')
    }
    
    if (androidClientMatch) {
      console.log(`✅ Android Client ID: ${androidClientMatch[1]}`)
    } else {
      console.log('❌ Android Client ID не знайдено')
    }
    
  } catch (error) {
    console.error('❌ Не вдалося прочитати mobile-auth.ts:', error.message)
  }
  
  console.log('\n🌐 Перевірка мережевих налаштувань:')
  
  // Отримання IP адреси
  try {
    const os = require('os')
    const interfaces = os.networkInterfaces()
    const addresses = []
    
    for (const name of Object.keys(interfaces)) {
      for (const interface of interfaces[name]) {
        if (interface.family === 'IPv4' && !interface.internal) {
          addresses.push(interface.address)
        }
      }
    }
    
    console.log('📍 Доступні IP адреси:')
    addresses.forEach(addr => {
      console.log(`   - ${addr}`)
      console.log(`   - Expo Go URI: exp://${addr}:8081/--/auth/callback`)
    })
    
    if (addresses.length === 0) {
      console.log('⚠️ Не знайдено зовнішніх IP адрес')
    }
    
  } catch (error) {
    console.error('❌ Помилка отримання IP адрес:', error.message)
  }
  
  console.log('\n🔧 Рекомендації для Google Cloud Console:')
  console.log('1. Відкрийте https://console.cloud.google.com/')
  console.log('2. Перейдіть до APIs & Services → Credentials')
  console.log('3. Знайдіть OAuth 2.0 Client ID для Web application')
  console.log('4. Додайте ці Authorized redirect URIs:')
  console.log('   - exp://localhost:8081/--/auth/callback')
  console.log('   - exp://127.0.0.1:8081/--/auth/callback')
  
  // Додаємо IP-специфічні URIs
  try {
    const os = require('os')
    const interfaces = os.networkInterfaces()
    
    for (const name of Object.keys(interfaces)) {
      for (const interface of interfaces[name]) {
        if (interface.family === 'IPv4' && !interface.internal) {
          console.log(`   - exp://${interface.address}:8081/--/auth/callback`)
        }
      }
    }
  } catch (error) {
    console.log('   - exp://[YOUR_IP]:8081/--/auth/callback')
  }
  
  console.log('\n🧪 Наступні кроки для тестування:')
  console.log('1. Оновіть Google Cloud Console з redirect URIs вище')
  console.log('2. Запустіть: expo start --clear')
  console.log('3. Відкрийте додаток в Expo Go')
  console.log('4. Натисніть "Увійти через Google"')
  console.log('5. Перевірте логи в консолі')
  
  console.log('\n📋 Файли для перевірки:')
  console.log('- GOOGLE_OAUTH_EXPO_GO_FIX.md - детальні інструкції')
  console.log('- lib/services/mobile-auth.ts - оновлений код')
  console.log('- GOOGLE_OAUTH_SETUP.md - початкові налаштування')
  
  console.log('\n✅ Перевірка завершена!')
}

// Запуск скрипта
main().catch(error => {
  console.error('❌ Помилка виконання скрипта:', error)
  process.exit(1)
})