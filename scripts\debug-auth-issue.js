/**
 * Скрипт для детальної діагностики проблеми з реєстрацією
 * Дата: 28.06.2025
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

// Клієнт з service role для адміністративних операцій
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Клієнт з anon key для тестування як звичайний користувач
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 ДІАГНОСТИКА ПРОБЛЕМИ З РЕЄСТРАЦІЄЮ');
console.log('=' .repeat(50));

async function checkDatabaseStructure() {
    console.log('\n📋 1. Перевірка структури бази даних...');
    
    try {
        // Перевіряємо таблицю profiles
        const { data: profiles, error: profilesError } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .limit(1);
            
        if (profilesError) {
            console.log('❌ Помилка доступу до таблиці profiles:', profilesError.message);
            return false;
        }
        
        console.log('✅ Таблиця profiles доступна');
        
        // Перевіряємо RLS політики
        const { data: policies, error: policiesError } = await supabaseAdmin
            .from('pg_policies')
            .select('*')
            .eq('tablename', 'profiles');
            
        if (policiesError) {
            console.log('⚠️ Не вдалося перевірити RLS політики:', policiesError.message);
        } else {
            console.log(`✅ Знайдено ${policies.length} RLS політик для таблиці profiles`);
            policies.forEach(policy => {
                console.log(`   - ${policy.policyname}: ${policy.cmd}`);
            });
        }
        
        return true;
    } catch (error) {
        console.log('❌ Помилка перевірки структури:', error.message);
        return false;
    }
}

async function checkTriggerFunction() {
    console.log('\n⚙️ 2. Перевірка тригер функції...');
    
    try {
        // Перевіряємо функцію handle_new_user
        const { data, error } = await supabaseAdmin
            .rpc('pg_get_functiondef', { 
                funcoid: 'public.handle_new_user()' 
            });
            
        if (error) {
            console.log('❌ Функція handle_new_user не знайдена:', error.message);
            return false;
        }
        
        console.log('✅ Функція handle_new_user існує');
        
        // Перевіряємо тригер
        const { data: triggers, error: triggerError } = await supabaseAdmin
            .from('information_schema.triggers')
            .select('*')
            .eq('trigger_name', 'on_auth_user_created');
            
        if (triggerError) {
            console.log('⚠️ Не вдалося перевірити тригер:', triggerError.message);
        } else if (triggers && triggers.length > 0) {
            console.log('✅ Тригер on_auth_user_created існує');
        } else {
            console.log('❌ Тригер on_auth_user_created НЕ знайдений');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log('❌ Помилка перевірки тригера:', error.message);
        return false;
    }
}

async function testUserCreation() {
    console.log('\n👤 3. Тестування створення користувача...');
    
    const testEmail = `test_${Date.now()}@example.com`;
    const testPassword = 'TestPass123!';
    const testName = 'Test User';
    
    try {
        console.log(`📧 Створюємо тестового користувача: ${testEmail}`);
        
        // Спробуємо створити користувача через anon клієнт (як в додатку)
        const { data, error } = await supabaseClient.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    name: testName
                }
            }
        });
        
        if (error) {
            console.log('❌ Помилка створення користувача:', error.message);
            console.log('📄 Деталі помилки:', JSON.stringify(error, null, 2));
            return false;
        }
        
        console.log('✅ Користувач створений успішно');
        console.log(`🆔 User ID: ${data.user?.id}`);
        console.log(`📧 Email: ${data.user?.email}`);
        
        // Перевіряємо, чи створився профіль
        if (data.user?.id) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Чекаємо 1 секунду
            
            const { data: profile, error: profileError } = await supabaseAdmin
                .from('profiles')
                .select('*')
                .eq('id', data.user.id)
                .single();
                
            if (profileError) {
                console.log('❌ Профіль не створився:', profileError.message);
                return false;
            }
            
            console.log('✅ Профіль створений автоматично');
            console.log('📄 Дані профілю:', JSON.stringify(profile, null, 2));
        }
        
        return true;
    } catch (error) {
        console.log('❌ Критична помилка:', error.message);
        return false;
    }
}

async function checkAuthSettings() {
    console.log('\n⚙️ 4. Перевірка налаштувань Auth...');
    
    try {
        // Перевіряємо налаштування через admin API
        const { data: settings, error } = await supabaseAdmin.auth.admin.getSettings();
        
        if (error) {
            console.log('⚠️ Не вдалося отримати налаштування Auth:', error.message);
        } else {
            console.log('✅ Налаштування Auth отримані');
            console.log(`📧 Email confirmations: ${settings.external?.email?.enabled}`);
            console.log(`🔐 Auto confirm: ${settings.external?.email?.autoconfirm}`);
        }
        
        return true;
    } catch (error) {
        console.log('⚠️ Помилка перевірки налаштувань:', error.message);
        return true; // Не критична помилка
    }
}

async function runDiagnostics() {
    console.log(`🌐 Supabase URL: ${supabaseUrl}`);
    console.log(`🔑 Anon Key: ${supabaseAnonKey?.substring(0, 20)}...`);
    console.log(`🔐 Service Key: ${supabaseServiceKey ? 'Присутній' : 'Відсутній'}`);
    
    const results = {
        database: await checkDatabaseStructure(),
        trigger: await checkTriggerFunction(),
        auth: await checkAuthSettings(),
        userCreation: await testUserCreation()
    };
    
    console.log('\n📊 РЕЗУЛЬТАТИ ДІАГНОСТИКИ:');
    console.log('=' .repeat(30));
    console.log(`📋 База даних: ${results.database ? '✅' : '❌'}`);
    console.log(`⚙️ Тригер: ${results.trigger ? '✅' : '❌'}`);
    console.log(`🔐 Auth: ${results.auth ? '✅' : '❌'}`);
    console.log(`👤 Створення користувача: ${results.userCreation ? '✅' : '❌'}`);
    
    if (results.userCreation) {
        console.log('\n🎉 ПРОБЛЕМА ВИРІШЕНА! Реєстрація працює правильно.');
    } else {
        console.log('\n🚨 ПРОБЛЕМА ЗАЛИШАЄТЬСЯ. Потрібне додаткове дослідження.');
    }
}

// Запускаємо діагностику
runDiagnostics().catch(console.error);
