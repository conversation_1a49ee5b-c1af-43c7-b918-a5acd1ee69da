import React from "react";
import { View, ScrollView, Pressable } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { H1, H2 } from "@/components/ui/typography";
import { useColorScheme } from "@/lib/useColorScheme";

export default function PaymentError() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { colorScheme } = useColorScheme();

  // Отримуємо параметри помилки
  const errorMessage = params.errorMessage as string || "Сталася помилка під час обробки платежу";
  const planName = params.planName as string || "Підписка";
  const planPrice = params.planPrice as string || "";
  const errorCode = params.errorCode as string || "";

  const handleRetryPayment = () => {
    // Повертаємося до екрану вибору планів для повторної спроби
    router.push("/subscription-plans");
  };

  const handleContactSupport = () => {
    // Тут можна додати логіку для зв'язку з підтримкою
    // Наприклад, відкрити email клієнт або чат
    console.log("Contact support requested");
  };

  const handleGoBack = () => {
    // Повертаємося до попереднього екрану
    router.back();
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" contentContainerStyle={{ flexGrow: 1 }}>
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-border">
          <Pressable onPress={handleGoBack}>
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={colorScheme === "dark" ? "#fff" : "#000"} 
            />
          </Pressable>
          <H2>Помилка оплати</H2>
          <View style={{ width: 24 }} />
        </View>

        <View className="flex-1 justify-center items-center p-6">
          {/* Error Icon */}
          <View className="w-24 h-24 bg-red-100 dark:bg-red-900/30 rounded-full items-center justify-center mb-6">
            <Ionicons name="close-circle" size={48} color="#ef4444" />
          </View>

          {/* Error Message */}
          <H1 className="text-center mb-4 text-red-600 dark:text-red-400">
            Оплата не пройшла
          </H1>

          <Text className="text-center text-muted-foreground mb-8 text-base leading-6">
            {errorMessage}
            {"\n\n"}
            Будь ласка, перевірте дані вашої картки та спробуйте ще раз.
          </Text>

          {/* Error Details */}
          <View className="w-full bg-card border border-border rounded-xl p-4 mb-8">
            <H2 className="mb-4">Деталі помилки</H2>
            
            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">План:</Text>
                <Text className="font-medium">{planName}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Сума:</Text>
                <Text className="font-medium">{planPrice}</Text>
              </View>
              
              {errorCode && (
                <View className="flex-row justify-between items-center">
                  <Text className="text-muted-foreground">Код помилки:</Text>
                  <Text className="font-mono text-sm text-red-600 dark:text-red-400">
                    {errorCode}
                  </Text>
                </View>
              )}
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Час:</Text>
                <Text className="font-medium">
                  {new Date().toLocaleString('uk-UA')}
                </Text>
              </View>
            </View>
          </View>

          {/* Common Issues */}
          <View className="w-full mb-8">
            <H2 className="mb-4 text-center">Можливі причини:</H2>
            
            <View className="space-y-3">
              {[
                "Недостатньо коштів на картці",
                "Неправильно введені дані картки",
                "Картка заблокована банком",
                "Проблеми з інтернет-з'єднанням",
                "Технічні проблеми платіжної системи"
              ].map((issue, index) => (
                <View key={index} className="flex-row items-center">
                  <Ionicons 
                    name="alert-circle-outline" 
                    size={20} 
                    color="#f59e0b" 
                    style={{ marginRight: 12 }}
                  />
                  <Text className="flex-1 text-sm">{issue}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Help Section */}
          <View className="w-full bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-8">
            <View className="flex-row items-center mb-2">
              <Ionicons name="information-circle" size={20} color="#3b82f6" />
              <Text className="ml-2 font-medium text-blue-700 dark:text-blue-300">
                Потрібна допомога?
              </Text>
            </View>
            <Text className="text-sm text-blue-600 dark:text-blue-400">
              Якщо проблема повторюється, зв'яжіться з нашою службою підтримки. 
              Ми допоможемо вирішити питання з оплатою.
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View className="p-6 border-t border-border space-y-3">
        <Button
          size="lg"
          variant="default"
          onPress={handleRetryPayment}
          className="w-full"
        >
          <Text>Спробувати ще раз</Text>
        </Button>
        
        <Button
          size="lg"
          variant="outline"
          onPress={handleContactSupport}
          className="w-full"
        >
          <View className="flex-row items-center">
            <Ionicons 
              name="chatbubble-outline" 
              size={20} 
              color={colorScheme === "dark" ? "#fff" : "#000"}
              style={{ marginRight: 8 }}
            />
            <Text>Зв'язатися з підтримкою</Text>
          </View>
        </Button>
      </View>
    </SafeAreaView>
  );
}