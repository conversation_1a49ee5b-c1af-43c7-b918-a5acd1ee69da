/**
 * Тестування Google OAuth в Expo Go
 * Цей скрипт допомагає діагностувати проблеми з redirect URI
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Тестування Google OAuth для Expo Go');
console.log('=====================================\n');

// Перевірка app.json конфігурації
function checkAppConfig() {
  console.log('📋 Перевірка app.json конфігурації...');
  
  try {
    const appJsonPath = path.join(process.cwd(), 'app.json');
    const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    console.log('✅ app.json знайдено');
    console.log(`   - Name: ${appConfig.expo.name}`);
    console.log(`   - Slug: ${appConfig.expo.slug}`);
    console.log(`   - Scheme: ${appConfig.expo.scheme}`);
    
    // Перевірка iOS конфігурації
    if (appConfig.expo.ios?.infoPlist?.CFBundleURLTypes) {
      console.log('✅ iOS URL schemes налаштовані');
      appConfig.expo.ios.infoPlist.CFBundleURLTypes.forEach((urlType, index) => {
        console.log(`   - URL Type ${index + 1}: ${urlType.CFBundleURLSchemes.join(', ')}`);
      });
    } else {
      console.log('⚠️  iOS URL schemes не налаштовані');
    }
    
    // Перевірка Android конфігурації
    if (appConfig.expo.android?.intentFilters) {
      console.log('✅ Android intent filters налаштовані');
      appConfig.expo.android.intentFilters.forEach((filter, index) => {
        if (filter.data) {
          filter.data.forEach(data => {
            console.log(`   - Intent Filter ${index + 1}: ${data.scheme}://${data.host || '*'}`);
          });
        }
      });
    } else {
      console.log('⚠️  Android intent filters не налаштовані');
    }
    
    return appConfig;
  } catch (error) {
    console.error('❌ Помилка читання app.json:', error.message);
    return null;
  }
}

// Генерація можливих redirect URIs
function generateRedirectURIs(appConfig) {
  console.log('\n🔗 Генерація можливих redirect URIs...');
  
  if (!appConfig) {
    console.log('❌ Неможливо згенерувати URIs без app.json');
    return;
  }
  
  const slug = appConfig.expo.slug;
  const scheme = appConfig.expo.scheme;
  
  console.log('📱 Development (Expo Go) URIs:');
  console.log(`   - https://auth.expo.io/@anonymous/${slug}`);
  console.log(`   - exp://*************:8081/--/auth/callback`);
  console.log(`   - exp://localhost:8081/--/auth/callback`);
  console.log(`   - exp://127.0.0.1:8081/--/auth/callback`);
  
  console.log('\n🚀 Production (Standalone) URIs:');
  console.log(`   - ${scheme}://auth/callback`);
  
  console.log('\n🌐 Web URIs (якщо потрібно):');
  console.log(`   - http://localhost:3000/auth/callback`);
  console.log(`   - https://your-domain.com/auth/callback`);
}

// Перевірка Google Client IDs
function checkGoogleConfig() {
  console.log('\n🔑 Перевірка Google OAuth конфігурації...');
  
  try {
    const authServicePath = path.join(process.cwd(), 'lib/services/mobile-auth.ts');
    const authServiceContent = fs.readFileSync(authServicePath, 'utf8');
    
    // Витягуємо Client IDs з коду
    const iosClientIdMatch = authServiceContent.match(/iosClientId:\s*'([^']+)'/);
    const androidClientIdMatch = authServiceContent.match(/androidClientId:\s*'([^']+)'/);
    const webClientIdMatch = authServiceContent.match(/webClientId:\s*'([^']+)'/);
    
    if (iosClientIdMatch) {
      console.log('✅ iOS Client ID знайдено');
      console.log(`   - ${iosClientIdMatch[1]}`);
    } else {
      console.log('❌ iOS Client ID не знайдено');
    }
    
    if (androidClientIdMatch) {
      console.log('✅ Android Client ID знайдено');
      console.log(`   - ${androidClientIdMatch[1]}`);
    } else {
      console.log('❌ Android Client ID не знайдено');
    }
    
    if (webClientIdMatch) {
      console.log('✅ Web Client ID знайдено');
      console.log(`   - ${webClientIdMatch[1]}`);
    } else {
      console.log('❌ Web Client ID не знайдено');
    }
    
  } catch (error) {
    console.error('❌ Помилка читання mobile-auth.ts:', error.message);
  }
}

// Перевірка Expo CLI та проєкту
function checkExpoStatus() {
  console.log('\n📱 Перевірка Expo статусу...');
  
  try {
    // Перевірка Expo CLI
    const expoVersion = execSync('expo --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Expo CLI версія: ${expoVersion}`);
    
    // Перевірка чи проєкт запущений
    try {
      const expoStatus = execSync('expo status', { encoding: 'utf8', timeout: 5000 });
      console.log('✅ Expo проєкт статус:');
      console.log(expoStatus);
    } catch (statusError) {
      console.log('⚠️  Expo проєкт не запущений або недоступний');
    }
    
  } catch (error) {
    console.error('❌ Expo CLI не встановлено або недоступно');
    console.log('💡 Встановіть Expo CLI: npm install -g @expo/cli');
  }
}

// Інструкції для налаштування Google Console
function showGoogleConsoleInstructions() {
  console.log('\n🔧 Інструкції для Google Cloud Console:');
  console.log('=====================================');
  console.log('1. Відкрийте https://console.cloud.google.com/');
  console.log('2. Виберіть ваш проєкт');
  console.log('3. APIs & Services → Credentials');
  console.log('4. Знайдіть Web Client ID');
  console.log('5. Додайте Authorized redirect URIs (див. вище)');
  console.log('6. Збережіть зміни');
  console.log('7. Зачекайте 5-10 хвилин для поширення');
  console.log('8. Перезапустіть Expo: expo start --clear');
}

// Поради для діагностики
function showTroubleshootingTips() {
  console.log('\n🔍 Поради для діагностики:');
  console.log('==========================');
  console.log('• Перевірте логи в Expo Metro bundler');
  console.log('• Шукайте "Generated redirect URI" в логах');
  console.log('• Переконайтеся що всі URIs додані в Google Console');
  console.log('• Зачекайте 10 хвилин після змін в Google Console');
  console.log('• Використовуйте expo start --clear для очищення кешу');
  console.log('• Перевірте інтернет-з\'єднання на пристрої');
}

// Основна функція
function main() {
  const appConfig = checkAppConfig();
  generateRedirectURIs(appConfig);
  checkGoogleConfig();
  checkExpoStatus();
  showGoogleConsoleInstructions();
  showTroubleshootingTips();
  
  console.log('\n✨ Тестування завершено!');
  console.log('📖 Детальні інструкції: GOOGLE_OAUTH_EXPO_GO_REDIRECT_URIS.md');
}

// Запуск
main();