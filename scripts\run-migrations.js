const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Помилка: Відсутні змінні середовища EXPO_PUBLIC_SUPABASE_URL або SUPABASE_SERVICE_ROLE_KEY');
  console.log('Додайте SUPABASE_SERVICE_ROLE_KEY до .env.local файлу');
  console.log('Service Role Key можна знайти в Supabase Dashboard → Settings → API');
  process.exit(1);
}

// Створюємо клієнт з Service Role Key для виконання міграцій
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runMigrations() {
  console.log('🚀 Початок виконання міграцій...\n');

  const migrations = [
    '001_create_profiles_table.sql',
    '002_create_app_tables.sql'
  ];

  for (const migrationFile of migrations) {
    console.log(`📄 Виконання міграції: ${migrationFile}`);
    
    try {
      // Читаємо SQL файл
      const sqlPath = path.join(__dirname, '..', 'supabase', 'migrations', migrationFile);
      const sql = await fs.readFile(sqlPath, 'utf8');
      
      // Розділяємо SQL на окремі команди (по крапці з комою)
      const commands = sql
        .split(/;\s*$/m)
        .filter(cmd => cmd.trim().length > 0)
        .map(cmd => cmd.trim() + ';');
      
      console.log(`   Знайдено ${commands.length} SQL команд`);
      
      // Виконуємо кожну команду окремо
      for (let i = 0; i < commands.length; i++) {
        const command = commands[i];
        
        // Пропускаємо коментарі
        if (command.trim().startsWith('--')) continue;
        
        try {
          const { error } = await supabase.rpc('exec_sql', {
            sql_query: command
          }).single();
          
          if (error) {
            // Якщо помилка про те, що таблиця/політика вже існує, це нормально
            if (error.message.includes('already exists')) {
              console.log(`   ⚠️  Пропущено (вже існує): ${command.substring(0, 50)}...`);
            } else {
              throw error;
            }
          } else {
            console.log(`   ✅ Виконано: ${command.substring(0, 50)}...`);
          }
        } catch (rpcError) {
          // Якщо RPC функція не існує, спробуємо прямий запит
          console.log(`   ℹ️  Спроба альтернативного методу для: ${command.substring(0, 50)}...`);
          
          // Для деяких команд можемо використати API Supabase
          if (command.includes('CREATE TABLE')) {
            console.log(`   ⚠️  Таблиці потрібно створити через Supabase Dashboard`);
          } else if (command.includes('CREATE POLICY')) {
            console.log(`   ⚠️  RLS політики потрібно створити через Supabase Dashboard`);
          } else {
            console.log(`   ⚠️  Команду потрібно виконати через SQL Editor в Supabase Dashboard`);
          }
        }
      }
      
      console.log(`✅ Міграція ${migrationFile} завершена\n`);
      
    } catch (error) {
      console.error(`❌ Помилка при виконанні міграції ${migrationFile}:`, error.message);
      console.log('\n💡 Рекомендація: Виконайте міграції вручну через Supabase Dashboard:');
      console.log('   1. Відкрийте Supabase Dashboard');
      console.log('   2. Перейдіть до SQL Editor');
      console.log('   3. Скопіюйте вміст файлів міграції та виконайте');
      console.log(`   4. Файли міграцій: supabase/migrations/${migrationFile}`);
    }
  }
}

async function checkTables() {
  console.log('\n🔍 Перевірка створених таблиць...\n');
  
  const tables = [
    'profiles',
    'projects', 
    'yarns',
    'calculations',
    'row_counters',
    'inspiration_gallery',
    'community_posts',
    'comments',
    'likes'
  ];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(0);
      
      if (error) {
        console.log(`❌ Таблиця ${table}: НЕ ЗНАЙДЕНО`);
      } else {
        console.log(`✅ Таблиця ${table}: існує`);
      }
    } catch (err) {
      console.log(`❌ Таблиця ${table}: помилка перевірки`);
    }
  }
}

// Виконуємо міграції
runMigrations()
  .then(() => checkTables())
  .then(() => {
    console.log('\n✨ Процес міграції завершено!');
    console.log('\n📝 Наступні кроки:');
    console.log('1. Якщо деякі таблиці не створені, виконайте SQL вручну через Supabase Dashboard');
    console.log('2. Налаштуйте Google OAuth в Supabase Dashboard → Authentication → Providers');
    console.log('3. Налаштуйте Wayforpay для тестових платежів');
  })
  .catch(error => {
    console.error('\n❌ Критична помилка:', error);
    process.exit(1);
  });