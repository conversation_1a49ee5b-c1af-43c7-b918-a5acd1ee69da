import React, { useState } from 'react'
import { View, ScrollView, Text, Pressable, TextInput } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useColorScheme } from 'nativewind'
import { colors } from '../../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'

export default function YarnScreen() {
  const { colorScheme } = useColorScheme()
  const isDark = colorScheme === 'dark'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')

  const filters = [
    { id: 'all', label: 'Вся пряжа', count: 24 },
    { id: 'lace', label: 'Мереживна', count: 3 },
    { id: 'dk', label: 'DK', count: 8 },
    { id: 'worsted', label: 'Worsted', count: 10 },
    { id: 'chunky', label: 'Товста', count: 3 }
  ]

  const yarnStash = [
    {
      id: '1',
      brand: 'Пехорка',
      name: 'Мериносовая',
      color: 'Білий (02)',
      colorHex: '#ffffff',
      weight: 'worsted',
      fiber: '50% мериносова вовна, 50% акрил',
      yardage: 200,
      weightPerSkein: 100,
      quantity: 6,
      totalYardage: 1200,
      costPerSkein: 85,
      totalCost: 510,
      purchaseDate: '15 листопада 2024',
      location: 'Полиця А, ряд 2',
      notes: 'Дуже м\'яка, підходить для дитячих речей'
    },
    {
      id: '2',
      brand: 'Alize',
      name: 'Baby Cotton',
      color: 'Рожевий (191)',
      colorHex: '#ffb6c1',
      weight: 'dk',
      fiber: '60% бавовна, 40% акрил',
      yardage: 165,
      weightPerSkein: 50,
      quantity: 4,
      totalYardage: 660,
      costPerSkein: 45,
      totalCost: 180,
      purchaseDate: '1 грудня 2024',
      location: 'Полиця B, ряд 1',
      notes: 'Ідеальна для дитячих речей'
    },
    {
      id: '3',
      brand: 'Regia',
      name: '4-ply',
      color: 'Мікс кольорів (9801)',
      colorHex: '#4169e1',
      weight: 'lace',
      fiber: '75% вовна, 25% поліамід',
      yardage: 420,
      weightPerSkein: 100,
      quantity: 2,
      totalYardage: 840,
      costPerSkein: 120,
      totalCost: 240,
      purchaseDate: '20 жовтня 2024',
      location: 'Полиця A, ряд 1',
      notes: 'Міцна пряжа для шкарпеток'
    },
    {
      id: '4',
      brand: 'Alize',
      name: 'Lanagold',
      color: 'Синій (17)',
      colorHex: '#1e3a8a',
      weight: 'worsted',
      fiber: '49% вовна, 51% акрил',
      yardage: 240,
      weightPerSkein: 100,
      quantity: 8,
      totalYardage: 1920,
      costPerSkein: 65,
      totalCost: 520,
      purchaseDate: '1 вересня 2024',
      location: 'Полиця C, ряд 1',
      notes: 'Для пледа, потрібно ще 4 мотки'
    }
  ]

  const getWeightLabel = (weight: string) => {
    switch (weight) {
      case 'lace': return 'Мереживна'
      case 'dk': return 'DK'
      case 'worsted': return 'Worsted'
      case 'chunky': return 'Товста'
      default: return weight
    }
  }

  const filteredYarn = yarnStash.filter(yarn => {
    const matchesSearch = yarn.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         yarn.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         yarn.color.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = selectedFilter === 'all' || yarn.weight === selectedFilter
    return matchesSearch && matchesFilter
  })

  const totalValue = yarnStash.reduce((sum, yarn) => sum + yarn.totalCost, 0)
  const totalYardage = yarnStash.reduce((sum, yarn) => sum + yarn.totalYardage, 0)

  return (
    <SafeAreaView 
      style={{ 
        flex: 1, 
        backgroundColor: isDark ? colors.dark.background : colors.light.background 
      }}
    >
      <ScrollView className="flex-1 px-4">
        {/* Header */}
        <View className="py-6">
          <View className="flex-row justify-between items-center mb-4">
            <View>
              <Text 
                className="text-3xl font-bold mb-2"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                Моя пряжа 🧶
              </Text>
              <Text 
                className="text-base"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                Управління запасами пряжі
              </Text>
            </View>
            <Pressable
              onPress={() => router.push('/yarn/add')}
              className="w-12 h-12 rounded-full items-center justify-center"
              style={{ backgroundColor: isDark ? colors.dark.primary : colors.light.primary }}
            >
              <Ionicons 
                name="add" 
                size={24} 
                color={isDark ? colors.dark.primaryForeground : colors.light.primaryForeground}
              />
            </Pressable>
          </View>

          {/* Statistics */}
          <View className="flex-row gap-3 mb-4">
            <View 
              className="flex-1 p-4 rounded-xl"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Text 
                className="text-sm font-medium mb-1"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                Загальна довжина
              </Text>
              <Text 
                className="text-xl font-bold"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                {totalYardage.toLocaleString()} м
              </Text>
            </View>
            <View 
              className="flex-1 p-4 rounded-xl"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Text 
                className="text-sm font-medium mb-1"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                Загальна вартість
              </Text>
              <Text 
                className="text-xl font-bold"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                {totalValue} ₴
              </Text>
            </View>
          </View>

          {/* Search */}
          <View 
            className="flex-row items-center px-4 py-3 rounded-xl mb-4"
            style={{ 
              backgroundColor: isDark ? colors.dark.card : colors.light.card,
              borderColor: isDark ? colors.dark.border : colors.light.border,
              borderWidth: 1
            }}
          >
            <Ionicons 
              name="search" 
              size={20} 
              color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
              style={{ marginRight: 12 }}
            />
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Пошук пряжі..."
              placeholderTextColor={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
              className="flex-1 text-base"
              style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
            />
          </View>

          {/* Filters */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-4">
            <View className="flex-row gap-2">
              {filters.map((filter) => (
                <Pressable
                  key={filter.id}
                  onPress={() => setSelectedFilter(filter.id)}
                  className="px-4 py-2 rounded-full flex-row items-center"
                  style={{ 
                    backgroundColor: selectedFilter === filter.id 
                      ? (isDark ? colors.dark.primary : colors.light.primary)
                      : (isDark ? colors.dark.card : colors.light.card),
                    borderColor: isDark ? colors.dark.border : colors.light.border,
                    borderWidth: selectedFilter === filter.id ? 0 : 1
                  }}
                >
                  <Text 
                    className="text-sm font-medium mr-1"
                    style={{ 
                      color: selectedFilter === filter.id 
                        ? (isDark ? colors.dark.primaryForeground : colors.light.primaryForeground)
                        : (isDark ? colors.dark.foreground : colors.light.foreground)
                    }}
                  >
                    {filter.label}
                  </Text>
                  <View 
                    className="w-5 h-5 rounded-full items-center justify-center"
                    style={{ 
                      backgroundColor: selectedFilter === filter.id 
                        ? (isDark ? colors.dark.primaryForeground : colors.light.primaryForeground)
                        : (isDark ? colors.dark.muted : colors.light.muted)
                    }}
                  >
                    <Text 
                      className="text-xs font-bold"
                      style={{ 
                        color: selectedFilter === filter.id 
                          ? (isDark ? colors.dark.primary : colors.light.primary)
                          : (isDark ? colors.dark.mutedForeground : colors.light.mutedForeground)
                      }}
                    >
                      {filter.count}
                    </Text>
                  </View>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Yarn List */}
        <View className="mb-8">
          {filteredYarn.length > 0 ? (
            <View className="gap-4">
              {filteredYarn.map((yarn) => (
                <Pressable
                  key={yarn.id}
                  onPress={() => router.push(`/yarn/${yarn.id}`)}
                  className="p-4 rounded-xl"
                  style={{ 
                    backgroundColor: isDark ? colors.dark.card : colors.light.card,
                    borderColor: isDark ? colors.dark.border : colors.light.border,
                    borderWidth: 1
                  }}
                >
                  {/* Yarn Header */}
                  <View className="flex-row items-start mb-3">
                    {/* Color Circle */}
                    <View 
                      className="w-12 h-12 rounded-full mr-3 border-2"
                      style={{ 
                        backgroundColor: yarn.colorHex,
                        borderColor: isDark ? colors.dark.border : colors.light.border
                      }}
                    />
                    
                    <View className="flex-1">
                      <Text 
                        className="text-lg font-semibold mb-1"
                        style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                      >
                        {yarn.brand} {yarn.name}
                      </Text>
                      <Text 
                        className="text-sm mb-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {yarn.color}
                      </Text>
                      <View 
                        className="px-2 py-1 rounded-full self-start"
                        style={{ backgroundColor: isDark ? colors.dark.muted : colors.light.muted }}
                      >
                        <Text 
                          className="text-xs font-medium"
                          style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                        >
                          {getWeightLabel(yarn.weight)}
                        </Text>
                      </View>
                    </View>

                    <View className="items-end">
                      <Text 
                        className="text-lg font-bold mb-1"
                        style={{ color: isDark ? colors.dark.primary : colors.light.primary }}
                      >
                        {yarn.quantity} мотків
                      </Text>
                      <Text 
                        className="text-sm"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {yarn.totalYardage} м
                      </Text>
                    </View>
                  </View>

                  {/* Yarn Details */}
                  <View className="gap-2 mb-3">
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="layers-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {yarn.fiber}
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="scale-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {yarn.yardage} м / {yarn.weightPerSkein} г на моток
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="location-outline" 
                        size={16} 
                        color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        style={{ marginRight: 8 }}
                      />
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        {yarn.location}
                      </Text>
                    </View>
                  </View>

                  {/* Cost and Purchase Info */}
                  <View className="flex-row justify-between items-center mb-3">
                    <View>
                      <Text 
                        className="text-sm"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        Вартість: {yarn.costPerSkein} ₴/моток
                      </Text>
                      <Text 
                        className="text-sm font-medium"
                        style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                      >
                        Загалом: {yarn.totalCost} ₴
                      </Text>
                    </View>
                    <Text 
                      className="text-xs"
                      style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                    >
                      Куплено: {yarn.purchaseDate}
                    </Text>
                  </View>

                  {/* Notes */}
                  {yarn.notes && (
                    <View 
                      className="p-3 rounded-lg"
                      style={{ backgroundColor: isDark ? colors.dark.muted : colors.light.muted }}
                    >
                      <Text 
                        className="text-sm"
                        style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                      >
                        💭 {yarn.notes}
                      </Text>
                    </View>
                  )}
                </Pressable>
              ))}
            </View>
          ) : (
            <View 
              className="p-8 rounded-xl items-center"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Ionicons 
                name="library-outline" 
                size={48} 
                color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                style={{ marginBottom: 12 }}
              />
              <Text 
                className="text-base font-medium mb-2"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                {searchQuery ? 'Пряжа не знайдена' : 'Поки що немає пряжі'}
              </Text>
              <Text 
                className="text-sm text-center mb-4"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                {searchQuery 
                  ? 'Спробуйте змінити пошуковий запит або фільтр'
                  : 'Додайте свою першу пряжу до каталогу'
                }
              </Text>
              {!searchQuery && (
                <Pressable
                  onPress={() => router.push('/yarn/add')}
                  className="px-6 py-3 rounded-full"
                  style={{ backgroundColor: isDark ? colors.dark.primary : colors.light.primary }}
                >
                  <Text 
                    className="text-sm font-medium"
                    style={{ color: isDark ? colors.dark.primaryForeground : colors.light.primaryForeground }}
                  >
                    Додати пряжу
                  </Text>
                </Pressable>
              )}
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text 
            className="text-xl font-semibold mb-4"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            Швидкі дії
          </Text>
          <View className="flex-row gap-3">
            <Pressable
              onPress={() => router.push('/yarn/scanner')}
              className="flex-1 p-4 rounded-xl items-center"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Ionicons 
                name="camera-outline" 
                size={24} 
                color={isDark ? colors.dark.primary : colors.light.primary}
                style={{ marginBottom: 8 }}
              />
              <Text 
                className="text-sm font-medium text-center"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                Сканувати етикетку
              </Text>
            </Pressable>
            <Pressable
              onPress={() => router.push('/yarn/export')}
              className="flex-1 p-4 rounded-xl items-center"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Ionicons 
                name="download-outline" 
                size={24} 
                color={isDark ? colors.dark.primary : colors.light.primary}
                style={{ marginBottom: 8 }}
              />
              <Text 
                className="text-sm font-medium text-center"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                Експорт каталогу
              </Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}