{"name": "expo-supabase-starter", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "start:expo-go": "expo start --go", "start:tunnel": "expo start --go --tunnel", "start:localhost": "expo start --go --localhost", "start:clear": "expo start --go --clear", "start:dev-client": "expo start --dev-client", "web": "expo start --web", "diagnose": "node scripts/network-diagnostics.js", "fix-expo": "scripts/fix-expo-issues.bat", "expo-launcher": "scripts/expo-launcher.bat", "android": "expo run:android", "ios": "expo run:ios", "build:development": "eas build --profile development", "build:preview": "eas build --profile preview", "build:production": "eas build --profile production", "build:ios": "eas build --platform ios", "build:android": "eas build --platform android", "build:dev:ios": "eas build --profile development --platform ios --simulator", "build:dev:android": "eas build --profile development --platform android", "build:dev:local:ios": "eas build --profile development --platform ios --simulator --local", "build:dev:local:android": "eas build --profile development --platform android --local", "install:dev:ios": "eas build:run -p ios --latest", "install:dev:android": "eas build:run -p android --latest", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "check-setup": "node ./scripts/check-setup.js", "lint": "eslint . --fix", "generate-colors": "node ./scripts/generate-colors.js && eslint ./constants/colors.ts --fix"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.0.1", "@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-google-signin/google-signin": "^14.0.1", "@rn-primitives/label": "^1.1.0", "@rn-primitives/radio-group": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/types": "^1.1.0", "@supabase/supabase-js": "^2.49.4", "aes-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "53.0.12", "expo-auth-session": "^6.2.0", "expo-constants": "~17.1.6", "expo-crypto": "^14.1.5", "expo-dev-client": "~5.2.1", "expo-font": "^13.3.1", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-local-authentication": "^16.0.4", "expo-router": "~5.1.0", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.9", "expo-web-browser": "^14.2.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-reanimated": "~3.17.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "tailwind-merge": "^3.2.0", "tailwindcss": "~3.4.17", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/aes-js": "^3.1.4", "@types/react": "~19.0.10", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "expo-build-properties": "^0.14.6", "prettier": "^3.5.3", "typescript": "~5.8.3"}, "expo": {"install": {"exclude": ["@supabase/supabase-js"]}}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}