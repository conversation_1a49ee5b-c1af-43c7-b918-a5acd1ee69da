# Вирішення проблем з Supabase MCP Server

## Проблема
Supabase MCP Server не підключається до VS Code, хоча в документації вказано, що він активний.

## Причини
1. **MCP сервери налаштовані глобально**, а не локально в проєкті
2. **Відсутній Service Role Key** для автентифікації
3. **Потрібен перезапуск VS Code** після змін конфігурації

## Рішення 1: Налаштування глобального MCP сервера

### Крок 1: Отримайте Service Role Key
1. Зайдіть на [Supabase Dashboard](https://supabase.com/dashboard)
2. Виберіть проект "expo-supabase-starter"
3. <PERSON><PERSON><PERSON> → API
4. Скопіюйте **service_role** ключ (УВАГА: НЕ anon key!)

### Крок 2: Додайте до глобальних налаштувань VS Code
Відкрийте `%APPDATA%\Code\User\settings.json` та додайте:

```json
{
  "modelContextProtocol.servers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-supabase"],
      "env": {
        "SUPABASE_URL": "https://xaeztaeqyjubmpgjxcgh.supabase.co",
        "SUPABASE_SERVICE_ROLE_KEY": "YOUR_SERVICE_ROLE_KEY_HERE"
      }
    }
  }
}
```

### Крок 3: Перезапустіть VS Code
- Закрийте VS Code повністю
- Відкрийте знову
- MCP сервер повинен автоматично підключитися

## Рішення 2: Використання Supabase CLI (Альтернатива)

Якщо MCP сервер не працює, використовуйте Supabase CLI:

### Ініціалізація
```bash
npx supabase init
```

### Підключення до проєкту
```bash
npx supabase link --project-ref xaeztaeqyjubmpgjxcgh
```

### Корисні команди
```bash
# Перегляд таблиць
npx supabase db dump --schema public

# Виконання SQL
npx supabase db execute --sql "SELECT * FROM profiles LIMIT 5"

# Генерація типів TypeScript
npx supabase gen types typescript --project-id xaeztaeqyjubmpgjxcgh > types/supabase.ts

# Перегляд міграцій
npx supabase db migrations list

# Застосування міграцій
npx supabase db push
```

## Рішення 3: Прямі API запити

Використовуйте Supabase клієнт безпосередньо в коді:

```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Приклад використання
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .limit(5)
```

## Перевірка підключення

### Через код
```typescript
// Створіть файл test-supabase.ts
import { supabase } from './lib/supabase'

async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .single()
    
    if (error) throw error
    console.log('✅ Supabase підключено успішно!')
    console.log('Кількість профілів:', data.count)
  } catch (error) {
    console.error('❌ Помилка підключення:', error)
  }
}

testConnection()
```

### Через Supabase CLI
```bash
# Перевірка підключення
npx supabase db remote list

# Перевірка статусу
npx supabase status
```

## Важливі примітки

### Безпека
- **НІКОЛИ** не комітьте Service Role Key в Git
- Використовуйте `.env.local` для зберігання ключів
- Service Role Key має повний доступ до бази даних

### Альтернативи MCP
1. **Supabase CLI** - повний функціонал через командний рядок
2. **Supabase Dashboard** - веб-інтерфейс для управління
3. **REST API** - прямі HTTP запити
4. **GraphQL** - через pg_graphql extension

### Поточний статус
- ✅ Supabase проєкт активний
- ✅ Змінні середовища налаштовані
- ✅ База даних створена з міграціями
- ❌ MCP сервер не підключений (потребує Service Role Key)
- ✅ Supabase CLI встановлено як альтернатива

## Наступні кроки

1. **Для MCP**: Отримайте Service Role Key та налаштуйте глобально
2. **Для розробки**: Використовуйте Supabase CLI або прямі API запити
3. **Для продакшену**: Налаштуйте CI/CD з GitHub Actions

---

*Створено: 21.06.2025*  
*Проєкт: Розрахуй і В'яжи*