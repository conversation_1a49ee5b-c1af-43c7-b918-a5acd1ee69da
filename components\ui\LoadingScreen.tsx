import React from 'react'
import { View, Text, ActivityIndicator } from 'react-native'

interface LoadingScreenProps {
  message?: string
}

export function LoadingScreen({ message = "Завантаження..." }: LoadingScreenProps) {
  return (
    <View className="flex-1 justify-center items-center bg-white dark:bg-gray-900">
      <ActivityIndicator size="large" color="#3b82f6" />
      <Text className="mt-4 text-lg text-gray-600 dark:text-gray-300 text-center">
        {message}
      </Text>
    </View>
  )
}