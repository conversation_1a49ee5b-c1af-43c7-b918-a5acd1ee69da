# Вирішення проблеми "Database error saving new user"

**Дата**: 28.06.2025  
**Статус**: ✅ ВИРІШЕНО  
**Критичність**: Висока  

## 📋 Опис проблеми

### Симптоми
- Помилка `Database error saving new user` при реєстрації нових користувачів
- Реєстрація працювала коректно з увімкненим email-підтвердженням
- Проблема з'явилася після відключення email-підтвердження (`enable_confirmations = false`)

### User Flow проекту
```
1. Вибір тарифного плану (subscription-plans.tsx)
2. Введення банківських реквізитів (PaymentScreen.tsx)  
3. Симуляція оплати
4. Реєстрація користувача (sign-up.tsx)
5. Автоматичний перехід до додатку
```

## 🔍 Процес діагностики

### Гіпотеза 1: Пошкоджене Docker середовище ❌
- **Дія**: Повний перезапуск Docker та скидання бази (`supabase stop`, `supabase db reset`)
- **Результат**: Помилка залишилася

### Гіпотеза 2: Помилка в SQL тригері ❌
- **Дія**: Видалення тригера `handle_new_user` та функції з міграцій
- **Результат**: Помилка залишилася, що довело проблему на стороні клієнта

### Гіпотеза 3: Некоректні дані на клієнті ❌
- **Дія**: Тимчасове видалення об'єкта `options` з `supabase.auth.signUp`
- **Результат**: Помилка залишилася

### ✅ Корінна причина: Конфлікт середовищ
**Виявлено**: Додаток працював з **продакшн базою даних Supabase**, а всі виправлення застосовувалися до **локального Supabase**.

## 🔧 Рішення

### 1. Відновлення триггера автоматичного створення профілів

**Файл**: `supabase/migrations/007_restore_user_trigger.sql`

```sql
-- Видаляємо старий триггер
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Створюємо покращену функцію
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  RAISE LOG 'Creating profile for new user: %', NEW.email;
  
  BEGIN
    INSERT INTO public.profiles (id, email, name, subscription_type, created_at, updated_at)
    VALUES (
      NEW.id, 
      NEW.email, 
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      'free',
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Profile created successfully for user: %', NEW.email;
    
  EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error creating profile for user %: %', NEW.email, SQLERRM;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Створюємо тригер
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### 2. Виправлення передачі метаданих

**Файл**: `context/supabase-provider.tsx`

```typescript
const signUp = async (
  email: string,
  password: string,
  options?: { [key: string]: any }
) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: options ? { data: options } : undefined,
  });

  if (error) {
    console.error("Error signing up:", error);
    throw error;
  }

  if (data.session && data.user) {
    setSession(data.session);
    console.log("User signed up:", data.user);
    
    // Fallback: створюємо профіль якщо триггер не спрацював
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', data.user.id)
        .single();
        
      if (profileError && profileError.code === 'PGRST116') {
        console.log("Creating profile manually as fallback");
        await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            name: options?.name || data.user.email!.split('@')[0],
            subscription_type: 'free'
          });
        console.log("Profile created manually");
      }
    } catch (fallbackError) {
      console.warn("Fallback profile creation failed:", fallbackError);
    }
  }
};
```

### 3. Налаштування локального середовища

**Файл**: `.env.local`

```env
# Supabase Configuration - LOCAL DEVELOPMENT (Android Emulator)
EXPO_PUBLIC_SUPABASE_URL=http://********:54321
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# PRODUCTION Supabase (commented out for local development)
# EXPO_PUBLIC_SUPABASE_URL=https://xaeztaeqyjubmpgjxcgh.supabase.co
# EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 4. Оптимізація User Flow

**Файл**: `app/screens/PaymentScreen.tsx`

```typescript
const handlePayment = async () => {
  // Validation...
  
  setIsProcessing(true);
  
  setTimeout(() => {
    setIsProcessing(false);
    
    if (!session) {
      // Якщо немає сесії, після оплати переходимо до реєстрації
      Alert.alert(
        "Оплата успішна!",
        "Тепер створіть акаунт для доступу до всіх функцій",
        [{
          text: "Створити акаунт",
          onPress: () => {
            router.push({
              pathname: "/sign-up",
              params: {
                selectedPlan: plan,
                planName: plan === "yearly" ? "Річна підписка" : "Місячна підписка",
                planPrice: `${price} грн`,
                planPeriod: plan === "yearly" ? "рік" : "місяць",
                paymentCompleted: "true",
              },
            });
          },
        }]
      );
    }
  }, 2000);
};
```

## 📊 Результати

### ✅ Успішна реєстрація
```
User signed up: {
  "id": "1ab2e03b-7d2b-40fb-a2b2-aeba504d3f6b",
  "email": "<EMAIL>", 
  "email_confirmed_at": "2025-06-28T10:18:42.116619534Z",
  "user_metadata": {
    "name": "Frtew",
    "subscription_plan": "yearly"
  }
}
```

### ✅ Автоматичне створення профілю
- Триггер `handle_new_user` спрацював успішно
- Профіль створений в таблиці `profiles`
- Fallback механізм готовий у разі потреби

### ✅ Правильна навігація
```
✅ User authenticated, navigating to calculators screen
📧 User email: <EMAIL>
🎯 Navigating to: /(protected)/calculators
```

## 🔧 Технічні деталі

### Мережеве підключення для Android емулятора
- **`127.0.0.1:54321`** - працює для хост-машини
- **`********:54321`** - працює для Android емулятора
- **`*************:54321`** - працює для реальних пристроїв в мережі

### RLS політики
```sql
-- Додано політику для INSERT
CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);
```

### Обробка помилок
- Триггер з `EXCEPTION` блоком не зупиняє процес реєстрації
- Fallback створення профілю на стороні клієнта
- Детальне логування для діагностики

## 🚀 Наступні кроки

### Для продакшн середовища:
1. Застосувати міграцію `007_restore_user_trigger.sql` до продакшн бази
2. Перемкнути `.env.local` на продакшн URL
3. Протестувати повний flow в продакшн

### Для подальшої розробки:
1. Додати unit тести для функції реєстрації
2. Створити E2E тести для повного User Flow
3. Додати моніторинг успішності реєстрацій

## 📚 Навчання

### Ключові висновки:
1. **Завжди перевіряйте середовище** - локальне vs продакшн
2. **Багаторівневий захист** - триггер + fallback + RLS
3. **Детальне логування** допомагає в діагностиці
4. **Android емулятор** має особливості мережевого підключення

### Інструменти діагностики:
- `scripts/debug-auth-issue.js` - комплексна діагностика
- `scripts/test-connection.js` - тест мережевого підключення
- Supabase Studio - моніторинг бази даних
- `supabase logs` - логи сервісів

---

**Автор**: AI Assistant  
**Дата створення**: 28.06.2025  
**Останнє оновлення**: 28.06.2025
