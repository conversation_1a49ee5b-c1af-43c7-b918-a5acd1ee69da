const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Відсутні змінні середовища EXPO_PUBLIC_SUPABASE_URL або SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function verifySetup() {
  console.log('🔍 Перевірка налаштувань проєкту "Розрахуй і В\'яжи"...\n')

  const results = {
    database: false,
    auth: false,
    storage: false,
    functions: false,
    total: 0
  }

  // 1. Перевірка таблиць бази даних
  console.log('📊 Перевірка таблиць бази даних...')
  const requiredTables = [
    'profiles',
    'projects', 
    'yarns',
    'calculations',
    'row_counters',
    'inspiration_gallery',
    'community_posts',
    'comments',
    'likes'
  ]

  let tablesOk = true
  for (const table of requiredTables) {
    try {
      const { error } = await supabase.from(table).select('*').limit(1)
      if (error) {
        console.log(`  ❌ Таблиця ${table}: ${error.message}`)
        tablesOk = false
      } else {
        console.log(`  ✅ Таблиця ${table}: OK`)
      }
    } catch (err) {
      console.log(`  ❌ Таблиця ${table}: ${err.message}`)
      tablesOk = false
    }
  }
  results.database = tablesOk
  if (tablesOk) results.total++

  // 2. Перевірка RLS політик
  console.log('\n🔒 Перевірка RLS політик...')
  try {
    const { data: policies } = await supabase.rpc('get_policies')
    if (policies && policies.length > 0) {
      console.log(`  ✅ Знайдено ${policies.length} RLS політик`)
      results.auth = true
      results.total++
    } else {
      console.log('  ⚠️  RLS політики не налаштовані')
    }
  } catch (err) {
    console.log('  ℹ️  Не вдалося перевірити RLS політики (потрібні додаткові права)')
  }

  // 3. Перевірка Storage buckets
  console.log('\n📦 Перевірка Storage buckets...')
  const requiredBuckets = ['avatars', 'project-images', 'yarn-images']
  let bucketsOk = true
  
  for (const bucket of requiredBuckets) {
    try {
      const { data, error } = await supabase.storage.getBucket(bucket)
      if (error || !data) {
        console.log(`  ❌ Bucket ${bucket}: не знайдено`)
        bucketsOk = false
      } else {
        console.log(`  ✅ Bucket ${bucket}: OK`)
      }
    } catch (err) {
      console.log(`  ❌ Bucket ${bucket}: ${err.message}`)
      bucketsOk = false
    }
  }
  results.storage = bucketsOk
  if (bucketsOk) results.total++

  // 4. Перевірка Edge Functions
  console.log('\n⚡ Перевірка Edge Functions...')
  const requiredFunctions = [
    'yarn-calculator',
    'needle-size-calculator',
    'wayforpay-webhook'
  ]
  
  console.log('  ℹ️  Edge Functions потрібно перевірити вручну в Supabase Dashboard')
  console.log('  Очікувані функції:')
  requiredFunctions.forEach(fn => {
    console.log(`    - ${fn}`)
  })

  // 5. Перевірка Google OAuth
  console.log('\n🔐 Перевірка Google OAuth...')
  console.log('  ℹ️  Перевірте в Supabase Dashboard > Authentication > Providers:')
  console.log('    - Google provider увімкнено')
  console.log('    - Client ID та Secret налаштовані')
  console.log('    - Redirect URL додано в Google Console')

  // Підсумок
  console.log('\n' + '='.repeat(50))
  console.log('📋 ПІДСУМОК ПЕРЕВІРКИ:')
  console.log('='.repeat(50))
  console.log(`✅ База даних: ${results.database ? 'OK' : 'FAILED'}`)
  console.log(`✅ Storage: ${results.storage ? 'OK' : 'FAILED'}`)
  console.log(`ℹ️  Auth: потрібна ручна перевірка`)
  console.log(`ℹ️  Edge Functions: потрібна ручна перевірка`)
  console.log(`ℹ️  Wayforpay: потрібна ручна перевірка`)
  console.log('\n' + '='.repeat(50))
  
  if (results.total < 2) {
    console.log('❌ Деякі компоненти не налаштовані правильно!')
    console.log('📖 Дивіться MIGRATION_EXECUTION_GUIDE.md для інструкцій')
  } else {
    console.log('✅ Основні компоненти налаштовані!')
    console.log('📖 Перевірте ручні налаштування згідно з MIGRATION_EXECUTION_GUIDE.md')
  }
}

// Запуск перевірки
verifySetup().catch(console.error)