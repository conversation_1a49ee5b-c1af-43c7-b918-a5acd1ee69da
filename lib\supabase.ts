import { createClient } from '@supabase/supabase-js'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Database } from '../types/supabase'

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
})

// Helper функції для роботи з базою даних

/**
 * Отримати профіль поточного користувача
 */
export async function getCurrentUserProfile() {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return null
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  
  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }
  
  return data
}

/**
 * Оновити профіль користувача
 */
export async function updateUserProfile(updates: Partial<Database['public']['Tables']['profiles']['Update']>) {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('No user logged in')
  
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', user.id)
    .select()
    .single()
  
  if (error) throw error
  
  return data
}

/**
 * Отримати проєкти користувача
 */
export async function getUserProjects() {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return []
  
  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .eq('user_id', user.id)
    .order('updated_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching projects:', error)
    return []
  }
  
  return data
}

/**
 * Створити новий проєкт
 */
export async function createProject(project: Database['public']['Tables']['projects']['Insert']) {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('No user logged in')
  
  const { data, error } = await supabase
    .from('projects')
    .insert({
      ...project,
      user_id: user.id,
    })
    .select()
    .single()
  
  if (error) throw error
  
  return data
}

/**
 * Отримати пряжу користувача
 */
export async function getUserYarns() {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return []
  
  const { data, error } = await supabase
    .from('yarns')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching yarns:', error)
    return []
  }
  
  return data
}

/**
 * Додати нову пряжу
 */
export async function addYarn(yarn: Database['public']['Tables']['yarns']['Insert']) {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('No user logged in')
  
  const { data, error } = await supabase
    .from('yarns')
    .insert({
      ...yarn,
      user_id: user.id,
    })
    .select()
    .single()
  
  if (error) throw error
  
  return data
}

/**
 * Зберегти розрахунок
 */
export async function saveCalculation(calculation: Database['public']['Tables']['calculations']['Insert']) {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('No user logged in')
  
  const { data, error } = await supabase
    .from('calculations')
    .insert({
      ...calculation,
      user_id: user.id,
    })
    .select()
    .single()
  
  if (error) throw error
  
  return data
}

/**
 * Отримати історію розрахунків
 */
export async function getCalculationHistory(type?: string) {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return []
  
  let query = supabase
    .from('calculations')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    .limit(50)
  
  if (type) {
    query = query.eq('type', type)
  }
  
  const { data, error } = await query
  
  if (error) {
    console.error('Error fetching calculations:', error)
    return []
  }
  
  return data
}

/**
 * Підписатися на зміни в реальному часі
 */
export function subscribeToProjectChanges(
  projectId: string,
  callback: (payload: any) => void
) {
  return supabase
    .channel(`project:${projectId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'projects',
        filter: `id=eq.${projectId}`,
      },
      callback
    )
    .subscribe()
}

/**
 * Завантажити зображення
 */
export async function uploadImage(
  bucket: string,
  path: string,
  file: File | Blob
) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false,
    })
  
  if (error) throw error
  
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)
  
  return publicUrl
}

/**
 * Видалити зображення
 */
export async function deleteImage(bucket: string, path: string) {
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path])
  
  if (error) throw error
}