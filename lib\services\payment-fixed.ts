import { supabase } from '../../config/supabase'
import * as Crypto from 'expo-crypto'
import * as WebBrowser from 'expo-web-browser'
import { useState } from 'react'

// Wayforpay тестові дані
const WAYFORPAY_CONFIG = {
  merchantAccount: 'test_merch_n1',
  merchantSecretKey: 'flk3409refn54t54t*FNJRET',
  merchantDomainName: 'knittingapp.com',
  serviceUrl: 'https://secure.wayforpay.com/pay'
}

interface PaymentData {
  merchantAccount: string
  merchantDomainName: string
  orderReference: string
  orderDate: number
  amount: number
  currency: string
  productName: string[]
  productCount: number[]
  productPrice: number[]
  merchantSignature: string
  returnUrl?: string
  serviceUrl?: string
}

// Генерація підпису для Wayforpay
async function generateSignature(data: Omit<PaymentData, 'merchantSignature'>): Promise<string> {
  const signatureString = [
    data.merchantAccount,
    data.merchantDomainName,
    data.orderReference,
    data.orderDate.toString(),
    data.amount.toString(),
    data.currency,
    ...data.productName,
    ...data.productCount.map(String),
    ...data.productPrice.map(String)
  ].join(';')
  
  const signatureWithKey = signatureString + ';' + WAYFORPAY_CONFIG.merchantSecretKey
  
  // Використовуємо expo-crypto для HMAC-SHA1
  const signature = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA1,
    signatureWithKey,
    { encoding: Crypto.CryptoEncoding.HEX }
  )
  
  return signature
}

export class PaymentServiceFixed {
  static async createPayment(amount: number, description: string) {
    try {
      console.log('🔄 Створюю платіж:', { amount, description })
      
      const orderReference = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const orderDate = Math.floor(Date.now() / 1000)
      
      const paymentData: Omit<PaymentData, 'merchantSignature'> = {
        merchantAccount: WAYFORPAY_CONFIG.merchantAccount,
        merchantDomainName: WAYFORPAY_CONFIG.merchantDomainName,
        orderReference,
        orderDate,
        amount,
        currency: 'UAH',
        productName: [description],
        productCount: [1],
        productPrice: [amount],
        returnUrl: 'knittingapp://payment-success',
        serviceUrl: 'knittingapp://payment-service'
      }
      
      const signature = await generateSignature(paymentData)
      
      const fullPaymentData: PaymentData = {
        ...paymentData,
        merchantSignature: signature
      }
      
      console.log('✅ Дані платежу підготовлені:', {
        orderReference,
        amount,
        signature: signature.substring(0, 10) + '...'
      })
      
      // Логуємо платіж БЕЗ використання payment_logs (обходимо кеш)
      await this.logPaymentDirectly(orderReference, amount, description, 'created')
      
      // Формуємо URL для Wayforpay
      const paymentUrl = this.buildPaymentUrl(fullPaymentData)
      
      console.log('🌐 Відкриваю платіжну сторінку...')
      
      // Відкриваємо платіжну сторінку
      const result = await WebBrowser.openBrowserAsync(paymentUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#f97316'
      })
      
      console.log('📱 Результат браузера:', result)
      
      return {
        success: true,
        orderReference,
        paymentUrl,
        browserResult: result
      }
      
    } catch (error) {
      console.error('❌ Помилка створення платежу:', error)
      throw new Error(`Помилка створення платежу: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  // Логування платежу напряму через SQL (обходимо кеш схеми)
  private static async logPaymentDirectly(
    orderReference: string, 
    amount: number, 
    description: string, 
    status: string
  ) {
    try {
      // Використовуємо RPC функцію замість прямого INSERT (обходимо кеш)
      const { data, error } = await supabase.rpc('log_payment_transaction', {
        p_order_reference: orderReference,
        p_amount: amount,
        p_description: description,
        p_status: status,
        p_payment_method: 'wayforpay'
      })
      
      if (error) {
        console.warn('⚠️ Не вдалося залогувати платіж (можливо, RPC функція не існує):', error.message)
        // Не кидаємо помилку, продовжуємо роботу
      } else {
        console.log('✅ Платіж залогований через RPC')
      }
    } catch (error) {
      console.warn('⚠️ Помилка логування платежу:', error instanceof Error ? error.message : String(error))
      // Не кидаємо помилку, продовжуємо роботу
    }
  }
  
  private static buildPaymentUrl(data: PaymentData): string {
    const params = new URLSearchParams()
    
    Object.entries(data).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          params.append(`${key}[${index}]`, item.toString())
        })
      } else {
        params.append(key, value.toString())
      }
    })
    
    return `${WAYFORPAY_CONFIG.serviceUrl}?${params.toString()}`
  }
  
  static async checkPaymentStatus(orderReference: string) {
    try {
      console.log('🔍 Перевіряю статус платежу:', orderReference)
      
      // Перевіряємо через profiles (обходимо payment_logs)
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        throw new Error('Користувач не авторизований')
      }
      
      // Перевіряємо підписку користувача
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type, subscription_expires_at')
        .eq('id', user.id)
        .single()
      
      if (error) {
        console.error('❌ Помилка перевірки профілю:', error)
        return { status: 'unknown', subscription: null }
      }
      
      const now = new Date()
      const expiresAt = profile.subscription_expires_at ? new Date(profile.subscription_expires_at) : null
      
      const isActive = profile.subscription_type !== 'free' && 
                      expiresAt && 
                      expiresAt > now
      
      return {
        status: isActive ? 'paid' : 'pending',
        subscription: {
          type: profile.subscription_type,
          expiresAt: profile.subscription_expires_at,
          isActive
        }
      }
      
    } catch (error) {
      console.error('❌ Помилка перевірки статусу:', error)
      return { status: 'error', subscription: null }
    }
  }
  
  static async getSubscriptionStatus() {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        return { isActive: false, type: 'free', expiresAt: null }
      }
      
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type, subscription_expires_at')
        .eq('id', user.id)
        .single()
      
      if (error) {
        console.error('❌ Помилка отримання підписки:', error)
        return { isActive: false, type: 'free', expiresAt: null }
      }
      
      const now = new Date()
      const expiresAt = profile.subscription_expires_at ? new Date(profile.subscription_expires_at) : null
      
      const isActive = profile.subscription_type !== 'free' && 
                      expiresAt && 
                      expiresAt > now
      
      return {
        isActive,
        type: profile.subscription_type || 'free',
        expiresAt: profile.subscription_expires_at
      }
      
    } catch (error) {
      console.error('❌ Помилка отримання статусу підписки:', error)
      return { isActive: false, type: 'free', expiresAt: null }
    }
  }
}

// React Hook для використання в компонентах
export const usePaymentFixed = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const createPayment = async (amount: number, description: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await PaymentServiceFixed.createPayment(amount, description)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Невідома помилка'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  const checkPaymentStatus = async (orderReference: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await PaymentServiceFixed.checkPaymentStatus(orderReference)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Невідома помилка'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  const getSubscriptionStatus = async () => {
    try {
      const result = await PaymentServiceFixed.getSubscriptionStatus()
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Невідома помилка'
      setError(errorMessage)
      return { isActive: false, type: 'free', expiresAt: null }
    }
  }
  
  return {
    createPayment,
    checkPaymentStatus,
    getSubscriptionStatus,
    loading,
    error
  }
}