const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
)

async function diagnoseMobileIssues() {
  console.log('🔍 Діагностика проблем мобільної платформи...\n')
  
  try {
    // 1. Перевірка OAuth провайдерів
    console.log('1️⃣ Перевірка OAuth провайдерів...')
    const { data: providers, error: providersError } = await supabase.auth.getSession()
    
    if (providersError) {
      console.error('❌ Помилка отримання сесії:', providersError.message)
    } else {
      console.log('✅ Supabase Auth доступний')
    }
    
    // 2. Перевірка тригера для нових користувачів
    console.log('\n2️⃣ Перевірка тригера handle_new_user...')
    const { data: functions, error: functionsError } = await supabase
      .rpc('version')
      .then(() => ({ data: 'OK', error: null }))
      .catch(err => ({ data: null, error: err }))
    
    if (functionsError) {
      console.error('❌ Помилка підключення до функцій:', functionsError.message)
    } else {
      console.log('✅ RPC функції доступні')
    }
    
    // 3. Тестування створення профілю
    console.log('\n3️⃣ Тестування структури profiles таблиці...')
    const { data: profilesStructure, error: structureError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1)
    
    if (structureError) {
      console.error('❌ Помилка доступу до profiles:', structureError.message)
    } else {
      console.log('✅ Таблиця profiles доступна')
      console.log('📊 Структура:', Object.keys(profilesStructure[0] || {}))
    }
    
    // 4. Перевірка OAuth конфігурації
    console.log('\n4️⃣ Рекомендації для мобільної платформи:')
    console.log('📱 Google OAuth на мобільних платформах потребує:')
    console.log('   - Expo AuthSession для нативної інтеграції')
    console.log('   - Або Google Sign-In SDK')
    console.log('   - Веб OAuth не працює в мобільних додатках')
    
    console.log('\n💡 Рішення:')
    console.log('   1. Встановити expo-auth-session')
    console.log('   2. Налаштувати нативний Google Sign-In')
    console.log('   3. Оновити OAuth flow для мобільних платформ')
    
    // 5. Перевірка deep linking
    console.log('\n5️⃣ Перевірка deep linking конфігурації...')
    console.log('📋 Схема: knittingapp://')
    console.log('📋 Bundle ID: com.knittingapp.calculator')
    console.log('📋 Redirect URI: com.knittingapp.calculator://auth/callback')
    
  } catch (error) {
    console.error('💥 Критична помилка:', error.message)
  }
}

diagnoseMobileIssues()