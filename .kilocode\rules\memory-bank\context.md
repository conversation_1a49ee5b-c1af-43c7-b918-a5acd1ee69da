# Поточний контекст розробки "Розрахуй і В'яжи"

## 📱 ПЛАТФОРМИ ТА ІНСТРУМЕНТИ

### Основні платформи розробки
**ПРІОРИТЕТ: Android та iOS мобільні додатки**
- React Native з Expo для нативних мобільних додатків
- Основний фокус: Android та iOS платформи
- Web додаток: додатковий (якщо потрібен для адмін-панелі)

### Пакетний менеджер
**Yarn** - основний пакетний менеджер проєкту
- Використовується `yarn.lock` для детермінованих установок
- Всі команди виконуються через Yarn CLI
- NPM MCP Server сумісний з Yarn (читає package.json)

## Поточний фокус розробки

### Активна фаза: MVP Foundation (Грудень 2024 - Березень 2025)
**Пріоритет**: Створення базової архітектури та основних функцій для мобільних платформ

#### Поточні завдання:
1. **Налаштування Offline-First архітектури**
   - Інтеграція WatermelonDB з React Native
   - Базова синхронізація з Supabase
   - Turbo Login механізм

2. **Розробка основних калькуляторів**
   - Калькулятор кількості пряжі для светрів
   - Калькулятор щільності в'язання
   - Калькулятор розмірів спиць
   - Edge Functions для серверних розрахунків

3. **Базове управління проєктами**
   - CRUD операції для проєктів
   - Лічильник рядів
   - Прив'язка пряжі до проєктів

## 🐞 Активне налагодження (Active Debugging)
**Дата**: 28.06.2025
**Проблема**: Критична помилка `Database error saving new user` при реєстрації нового користувача.

### Історія налагодження:
1.  **Гіпотеза 1: Пошкоджене середовище Docker (Спростовано)**
   - **Дія**: Повний перезапуск Docker та скидання бази даних (`supabase stop`, `supabase db reset`).
   - **Результат**: Помилка залишилася.

2.  **Гіпотеза 2: Помилка в SQL тригері `handle_new_user` (Спростовано)**
   - **Дія**: Повне видалення тригера та функції з міграцій.
   - **Результат**: Помилка залишилася, що доводить проблему на стороні клієнта або конфігурації Auth.

3.  **Гіпотеза 3: Некоректні дані на клієнті (В процесі тестування)**
   - **Дія**: Тимчасово видалено об'єкт `options` з виклику `supabase.auth.signUp` у `context/supabase-provider.tsx`.
   - **Статус**: Очікуємо на результати тестування від користувача.

### 💡 Ключова нова інформація:
- **Попередня поведінка**: Реєстрація працювала коректно, коли було ввімкнене підтвердження по email.
- **Поточна поведінка**: Помилка почала з'являтися після відключення підтвердження email для спрощення процесу реєстрації.
- **Нова гіпотеза**: Проблема може бути в налаштуваннях Supabase Auth, пов'язаних з автоматичним підтвердженням користувачів, а не в самому коді. Можливо, `raw_user_meta_data` не створюється або не оновлюється належним чином без кроку підтвердження.

### Наступні кроки по налагодженню:
1.  Проаналізувати результат поточного тесту (без `options.data`).
2.  Якщо помилка залишається, дослідити налаштування Supabase Auth:
   - Перевірити, чи ввімкнено `Enable auto-confirmations`.
   - Дослідити, як відключення підтвердження впливає на життєвий цикл створення користувача та пов'язаних з ним даних у `public.profiles`.
3.  Розглянути альтернативний спосіб створення профілю, наприклад, через Edge Function, що викликається після успішної реєстрації.

## Завершені функції (Completed Features)

### ✅ Інфраструктура та налаштування
- **React Native (Expo) проєкт** - базова структура створена
- **TypeScript конфігурація** - строга типізація налаштована
- **NativeWind інтеграція** - TailwindCSS для стилізації
- **Supabase підключення** - автентифікація та база даних
- **Базова навігація** - React Navigation з tab та stack навігаторами

### ✅ Автентифікація (21.06.2025) - ЗАВЕРШЕНО
- **Supabase Auth** - email/password та соціальні мережі
- **Google OAuth** - налаштовано з усіма Client ID (Android, iOS, Web)
- **Біометрична автентифікація** - Touch ID/Face ID через Expo LocalAuthentication
- **Захищені маршрути** - розділення на публічні та приватні екрани
- **Стан автентифікації** - глобальне управління через Context
- **Автоматичне створення профілів** - тригер для нових користувачів
- **Deep linking** - налаштовано для OAuth callbacks

### ✅ Виправлення помилок запуску (21.06.2025) - ЗАВЕРШЕНО
- **Встановлено відсутні залежності**:
  - expo-build-properties@~8.0.2
  - expo-crypto@14.1.5
  - expo-web-browser@14.2.0
- **Оновлено Payment Service** - інтеграція з expo-crypto та expo-web-browser
- **Виправлено TypeScript помилки** - правильна обробка WebBrowser результатів
- **Очищено кеш Expo** - вирішення проблем з React Context

### ✅ База даних (21.06.2025) - ЗАВЕРШЕНО
- **Таблиця profiles** - профілі користувачів з підпискою
- **Таблиця projects** - проєкти в'язання
- **Таблиця yarns** - каталог пряжі
- **Таблиця calculations** - збережені розрахунки
- **Таблиця row_counters** - лічильники рядів
- **Таблиця inspiration_gallery** - галерея натхнення
- **Таблиця community_posts** - пости спільноти
- **Таблиця comments** - коментарі до постів
- **Таблиця likes** - лайки постів
- **RLS політики** - налаштовані для всіх таблиць
- **Індекси** - оптимізація запитів
- **Тригери** - автоматичне оновлення updated_at

### ✅ UI компоненти
- **Базові UI компоненти** - Button, Input, Text, Form
- **Тема та кольори** - консистентна кольорова схема
- **Адаптивний дизайн** - підтримка темної/світлої теми
- **Екрани автентифікації** - Welcome, Sign In, Sign Up, Forgot Password
- **Екрани підписки** - Subscription Plans, Payment
- **Правові екрани** - Terms of Service, Privacy Policy

### ✅ Android емулятор (24.06.2025) - ПОВНІСТЮ ВИРІШЕНО
- **Автоматизована діагностика** - створено скрипт `scripts/android-emulator-diagnostic.bat`
- **Корінна причина виявлена** - емулятор не був запущений (не ADB проблеми)
- **Успішне рішення** - `emulator -avd Pixel_6 -no-snapshot-load`
- **Підтверджена готовність** - всі 6 діагностичних кроків пройшли ✅
- **Повна документація** - `ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md`
- **"5-хвилинне правило"** - швидкий алгоритм вирішення проблем
- **Готовність до розробки** - 100% функціональна мобільна платформа

### ✅ MCP інтеграція (Червень 2025) - АКТИВНА ФАЗА
**Статус**: 🟡 5/6 серверів активні, 1 потребує налаштування
**Дата останньої перевірки**: 21.06.2025, 18:25

#### Активні MCP сервери (5/6):
- **Context7 MCP Server** ✅ - документація React Native, Supabase, WatermelonDB, Expo
- **NPM Registry MCP Server** ✅ - аналіз пакетів та залежностей (сумісний з Yarn)
- **File System MCP Server** ✅ - робота з файлами (.ts,.tsx,.js,.jsx,.json,.md,.lock)
- **Supabase MCP Server** ❌ - потребує Service Role Key (альтернатива: Supabase JS Client працює)
- **Figma MCP Server** ✅ - конвертація дизайнів у React Native компоненти
- **GitHub MCP Server** ✅ - автоматизація workflow, Issues, Pull Requests

**Примітка**: Supabase підключення працює через JavaScript клієнт. MCP сервер опціональний.

## Активні фази розробки (Active Development Phases)

### 🔄 Фаза 1: WatermelonDB інтеграція (В процесі)
**Статус**: 70% завершено
**Очікувана дата завершення**: Січень 2025

#### Завершено:
- Базова схема бази даних
- Моделі для Users, Projects, Calculations
- Початкова конфігурація WatermelonDB
- Структура таблиць в Supabase

#### В процесі:
- Синхронізація з Supabase
- Міграції бази даних
- Оптимізація запитів

#### Наступні кроки:
- Turbo Login реалізація
- Conflict resolution стратегії
- Тестування офлайн-режиму

### 🔄 Фаза 2: Калькулятори пряжі (В процесі)
**Статус**: 60% завершено
**Очікувана дата завершення**: Січень 2025

#### Завершено:
- ✅ **UI екран з 29 калькуляторами** - повна структура створена
- ✅ **Категоризація калькуляторів** - 10 логічних категорій
- ✅ **Акордеон-навігація** - зручний інтерфейс користувача
- ✅ **Система обраного** - швидкий доступ до улюблених калькуляторів
- Базова структура Edge Functions
- Прототип калькулятора светрів

#### В процесі:
- Реалізація першого функціонального калькулятора
- Edge Functions для серверних розрахунків
- Валідація вхідних даних

#### Заплановано:
- Інтеграція з проєктами
- Офлайн-кешування результатів
- Збереження історії розрахунків

### 🔄 Фаза 3: Управління проєктами (Планується)
**Статус**: 30% завершено
**Очікувана дата початку**: Березень 2025

#### Завершено:
- Базова модель проєктів
- CRUD операції
- Структура бази даних

#### Заплановано:
- Лічильник рядів з персистентністю
- Фото-галерея для проєктів
- Статуси проєктів
- Нотатки та теги

## Нещодавні зміни (21.06.2025)

### Автентифікація та база даних
1. **Створено повну систему автентифікації**:
   - Email/password реєстрація та вхід
   - Google OAuth інтеграція
   - Біометрична автентифікація
   - Відновлення паролю

2. **Налаштовано базу даних**:
   - 9 основних таблиць для функціональності додатку
   - RLS політики для безпеки даних
   - Автоматичне створення профілів користувачів
   - Індекси для оптимізації

3. **Створено документацію**:
   - `AUTH_SETUP_INSTRUCTIONS.md` - інструкції з автентифікації
   - `GOOGLE_OAUTH_SETUP.md` - налаштування Google OAuth
   - `SETUP_COMPLETE_GUIDE.md` - повний гайд налаштування
   - SQL міграції в `supabase/migrations/`

4. **Оновлено конфігурацію**:
   - `app.json` - deep linking для OAuth
   - Схема URL: `knittingapp`
   - Bundle ID: `com.knittingapp.calculator`

### ✅ Платіжна інтеграція (21.06.2025) - ЗАВЕРШЕНО
1. **Wayforpay інтеграція**:
   - Edge Function для webhook обробки платежів
   - Сервіс платежів з підтримкою тестового режиму
   - UI екран вибору підписки
   - Тестові дані картки для розробки
   - HMAC-SHA1 підписи через expo-crypto
   - Відкриття платіжної сторінки через expo-web-browser

2. **Створені файли**:
   - `supabase/functions/wayforpay-webhook/index.ts` - webhook handler
   - `lib/services/payment.ts` - платіжний сервіс
   - `app/screens/PaymentScreen.tsx` - екран підписки
   - `components/ui/card.tsx` - UI компонент

3. **Допоміжні скрипти та документація**:
   - `scripts/run-migrations.js` - автоматичне виконання міграцій
   - `scripts/verify-setup.js` - перевірка налаштувань
   - `MIGRATION_EXECUTION_GUIDE.md` - покроковий гайд
   - `SETUP_COMPLETION_SUMMARY.md` - підсумок виконаних завдань
   - `EXPO_BUILD_PROPERTIES_FIX.md` - виправлення помилки залежностей
   - `EXPO_RUNTIME_FIX.md` - керівництво з виправлення помилок запуску
   - `FIXES_SUMMARY.md` - підсумок всіх виправлень

## Нещодавні зміни (24.06.2025)

### ✅ Android емулятор - ПРОБЛЕМА ПОВНІСТЮ ВИРІШЕНА
1. **Автоматизована діагностика**:
   - Створено скрипт `scripts/android-emulator-diagnostic.bat` для швидкої діагностики
   - Виконано повний аналіз системи: ADB, емулятор, порти, мережа, додаток
   - Виявлено корінну причину: емулятор просто не був запущений

2. **Успішне рішення**:
   - Запуск емулятора: `emulator -avd Pixel_6 -no-snapshot-load`
   - Підтвердження підключення: `adb devices` показав `emulator-5554 device`
   - Успішна збірка додатку: `npx expo start --clear --android`
   - Всі 6 діагностичних кроків пройшли успішно ✅

3. **Результат**:
   - Додаток працює стабільно на Android емуляторі
   - Автентифікація та навігація функціонують коректно
   - Supabase інтеграція працює без проблем
   - Мережеве підключення: 3.52ms ping до 8.8.8.8
   - Створено повну документацію troubleshooting

4. **Ключові навчання**:
   - **"5-хвилинне правило"**: Якщо емулятор offline > 5 хвилин - робити повний перезапуск
   - Автоматизована діагностика економить час та точно виявляє проблеми
   - Простота рішення: часто проблема в тому, що емулятор просто не запущений

### ✅ Навігація після автентифікації (24.06.2025) - ЗАВЕРШЕНО
1. **Вирішено критичну проблему навігації**:
   - Виправлено конфлікт дублікату екранів у навігації
   - Усунено помилку "A navigator can only contain 'Screen', 'Group' or 'Navigator' components"
   - Налаштовано правильну структуру protected routes

2. **Оптимізовано структуру навігації**:
   - `app/(protected)/_layout.tsx` - основний layout для захищених екранів
   - `app/(protected)/index.tsx` - головний екран після входу
   - `app/(protected)/calculators.tsx` - екран калькуляторів
   - Видалено дублікат `app/(protected)/(tabs)/` структури

3. **Результат**:
   - Плавний перехід від автентифікації до головного екрану
   - Стабільна навігація між захищеними екранами
   - Відсутність помилок навігації в консолі

### ✅ Екран калькуляторів з 29 калькуляторами (24.06.2025) - ЗАВЕРШЕНО
1. **Створено повноцінний екран калькуляторів**:
   - 29 спеціалізованих калькуляторів для в'язання
   - Організовано в 10 логічних категорій
   - Акордеон-навігація для зручності використання

2. **Структура калькуляторів по категоріях**:
   - **Основи в'язання** (4 калькулятори): Кількість пряжі, Розмір спиць, Щільність в'язання, Заміна пряжі
   - **Одяг** (5 калькуляторів): Светри, Кардигани, Жилети, Сукні, Спідниці
   - **Аксесуари** (4 калькулятори): Шапки, Шарфи, Рукавички, Шкарпетки
   - **Дитячий одяг** (3 калькулятори): Дитячі светри, Комбінезони, Пледи
   - **Домашній текстиль** (3 калькулятори): Подушки, Покривала, Килимки
   - **Іграшки** (2 калькулятори): Амігурумі, М'які іграшки
   - **Сумки та кошики** (2 калькулятори): Сумки, Кошики
   - **Прикраси** (2 калькулятори): Брошки, Сережки
   - **Технічні розрахунки** (3 калькулятори): Скорочення петель, Прибавлення петель, Розрахунок кутів
   - **Спеціальні техніки** (1 калькулятор): Жаккардові візерунки

3. **Функціональність**:
   - Система обраного (favorites) для швидкого доступу
   - Пошук по назві калькулятора
   - Адаптивний дизайн з NativeWind стилізацією
   - Готовність до інтеграції з бекендом

4. **Технічні деталі**:
   - TypeScript типізація для всіх калькуляторів
   - Консистентна структура даних
   - Підготовлено для Edge Functions інтеграції
   - Офлайн-готовність для майбутньої WatermelonDB інтеграції

## Наступні кроки (Next Steps)

### 🚀 ГОТОВНІСТЬ ДО АКТИВНОЇ РОЗРОБКИ ФУНКЦІЙ (24.06.2025)
**СТАТУС**: Повний User Flow працює - від автентифікації до калькуляторів

**Досягнення**:
- ✅ Android платформа повністю налаштована
- ✅ Навігація після автентифікації працює стабільно
- ✅ Екран з 29 калькуляторами створено та готовий
- ✅ Повний User Flow: Вхід → Головний екран → Калькулятори

**Детальний план розробки**: [`DEVELOPMENT_ROADMAP_POST_ANDROID.md`](../../../DEVELOPMENT_ROADMAP_POST_ANDROID.md)

### Фаза 1: Реалізація першого функціонального калькулятора (Тиждень 1-2)
**ПРІОРИТЕТ**: Створити повний цикл роботи з калькулятором пряжі

1. **UI форма калькулятора**
   - Детальна форма для калькулятора кількості пряжі
   - Валідація даних та обробка помилок
   - Результати з рекомендаціями та візуалізацією

2. **Edge Function для розрахунків**
   - `supabase/functions/yarn-calculator/index.ts` - серверна логіка
   - Валідація параметрів та точні розрахунки
   - CORS налаштування та обробка помилок

3. **Офлайн-логіка**
   - `lib/calculations/yarnCalculator.ts` - локальні розрахунки
   - `hooks/useYarnCalculator.ts` - React hook з fallback
   - Збереження результатів в WatermelonDB

### Фаза 2: Розширення функціональності калькуляторів (Тиждень 3-4)
**Мета**: Додати ще 2-3 функціональних калькулятори

1. **Калькулятор розміру спиць**
   - Форма з параметрами пряжі та бажаної щільності
   - Рекомендації розмірів спиць для різних типів пряжі
   - Таблиця відповідності розмірів (US, EU, UK)

2. **Калькулятор щільності в'язання**
   - Розрахунок петель та рядів на дюйм/см
   - Конвертер між різними одиницями виміру
   - Поради для досягнення потрібної щільності

3. **Система збереження розрахунків**
   - Історія всіх виконаних розрахунків
   - Можливість додавання нотаток до результатів
   - Експорт результатів для використання в проєктах

### Фаза 3: WatermelonDB синхронізація (Тиждень 5-6)
**Мета**: Налаштувати повну синхронізацію між локальною та серверною базами

1. **Sync Adapter**
   - `database/sync.ts` - синхронізація WatermelonDB ↔ Supabase
   - Pull/Push changes механізм
   - Conflict resolution

2. **Supabase RPC Functions**
   - `supabase/migrations/003_sync_functions.sql` - SQL функції
   - Оптимізовані запити для синхронізації
   - Безпека через RLS

### Довгострокові цілі (Серпень-Вересень 2025)
1. **Розширення калькуляторів** - 10+ спеціалізованих калькуляторів
2. **Спільнота та соціальні функції** - пости, коментарі, лайки
3. **Оптимізація та тестування** - E2E тести, performance
4. **Підготовка до релізу** - App Store та Google Play

## Технічний борг

### Високий пріоритет
1. **Синхронізація даних** - потрібна повна реалізація
2. **Офлайн-режим** - кешування та локальне зберігання
3. **Обробка помилок** - централізована система

### Середній пріоритет
1. **Тестове покриття** - unit та integration тести
2. **Документація коду** - JSDoc коментарі
3. **Оптимізація зображень** - стиснення та lazy loading

### Низький пріоритет
1. **Аналітика** - інтеграція з сервісом аналітики
2. **Push-повідомлення** - OneSignal інтеграція
3. **Локалізація** - підтримка інших мов

## Команда та ресурси

### Поточна команда
- **1 Full-stack розробник** - React Native + Supabase
- **1 UI/UX дизайнер** - дизайн та користувацький досвід
- **1 Product Owner** - продуктові рішення

### Використані ресурси
- **Supabase** - безкоштовний план
- **Expo** - безкоштовний план
- **Google Cloud** - OAuth налаштування

---

*Останнє оновлення: 24.06.2025, 23:30*
*Наступне оновлення: Щотижня по п'ятницях*
*Відповідальний: Product Owner*

## Історія змін
- **24.06.2025 (23:30)** - КРИТИЧНИЙ ПРОРИВ: Повний User Flow готовий:
  - ✅ Вирішено проблему навігації після автентифікації
  - ✅ Створено екран з 29 калькуляторами в 10 категоріях
  - ✅ Виправлено конфлікт дублікату екранів навігації
  - ✅ Повний User Flow працює: Вхід → Головний екран → Калькулятори
  - ✅ Акордеон-навігація та система обраного реалізовані
  - ✅ Готовність до інтеграції з Edge Functions та WatermelonDB
  - Оновлено статус Фази 2 (Калькулятори) з 20% до 60% завершення
  - Переорієнтовано наступні кроки на реалізацію функціональних калькуляторів
- **24.06.2025 (ранок)** - КРИТИЧНЕ ВИРІШЕННЯ: Android емулятор повністю налаштований:
  - Вирішено проблему "offline" стану емулятора Pixel_6
  - Успішна збірка додатку за 5780ms (1497 модулів)
  - Автентифікація та навігація працюють на мобільній платформі
  - Створено повну документацію troubleshooting для майбутніх проблем
  - Готовність до активної розробки функцій на Android платформі
  - Ключове навчання: повний перезапуск емулятора ефективніший за ADB команди
- **21.06.2025** - Завершено автентифікацію, базу даних, платіжну інтеграцію та виправлено помилки запуску:
  - Створено повну систему автентифікації (email, Google OAuth, біометрія)
  - Налаштовано всі необхідні таблиці бази даних з RLS
  - Інтегровано Wayforpay для платежів з тестовим режимом
  - Створено автоматизовані скрипти для міграцій та верифікації
  - Створено детальну документацію для налаштування
  - Оновлено статус WatermelonDB інтеграції на 70%
  - Виправлено помилки залежностей (expo-build-properties, expo-crypto, expo-web-browser)
  - Вирішено проблему React Context через очищення кешу
- **20.06.2025** - Верифікація та оновлення MCP серверів
- **19.06.2025** - Впроваджено критичні правила управління документацією
- **18.06.2025** - Оновлено MCP конфігурацію та тестування
- **17.06.2025** - Початкова версія контексту