# Швидкий старт - "Розрахуй і В'яжи"

Цей документ містить все необхідне для налаштування, запуску та тестування проєкту "Розрахуй і В'яжи".

## 1. Початкове налаштування

### Крок 1: Встановлення залежностей
Переконайтеся, що у вас встановлено **Node.js**, **Yarn** та **Docker Desktop**.

### Крок 2: Встановлення Supabase CLI
Найкращий спосіб для Windows — через **Scoop**.

```powershell
# 1. Дозволити виконання скриптів (якщо ще не зроблено)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 2. Встановити Scoop
irm get.scoop.sh | iex

# 3. Встановити Supabase CLI
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

### Крок 3: Клонування та встановлення залежностей проєкту
```bash
git clone <repository-url>
cd expo-supabase-starter
yarn install
```

### Крок 4: Налаштування змінних середовища
1.  Створіть файл `.env.local` в корені проєкту, скопіювавши `.env.example`.
2.  Запустіть локальне середовище Supabase:
    ```bash
    supabase start
    ```
3.  Скопіюйте `API URL` та `anon key` з виводу команди в `.env.local`.

## 2. Запуск локального середовища

### Крок 1: Запуск Supabase
```bash
# Ця команда запустить всі сервіси Supabase в Docker
supabase start
```

### Крок 2: Запуск додатку Expo
```bash
# Для Android емулятора
yarn android

# Для iOS симулятора
yarn ios

# Для Expo Go на фізичному пристрої
yarn start
```

## 3. Налаштування автентифікації

### Крок 1: Створення таблиць в базі даних
Після запуску Supabase, виконайте міграції для створення необхідних таблиць.
```bash
supabase db reset
```
Ця команда застосує всі SQL файли з папки `supabase/migrations`.

### Крок 2: Налаштування Google OAuth
1.  **В Supabase Dashboard:**
    *   Перейдіть в **Authentication** → **Providers** → **Google**.
    *   Вставте ваші `Client ID` та `Client Secret` з Google Cloud Console.
    *   Скопіюйте **Redirect URL**.

2.  **В Google Cloud Console:**
    *   Перейдіть до вашого OAuth клієнта.
    *   В **Authorized redirect URIs** додайте URL, скопійований з Supabase.

## 4. Тестування та налагодження

### Корисні команди
```bash
# Перевірка стану Docker контейнерів
docker ps

# Перегляд логів конкретного контейнера
docker logs <container_id_or_name>

# Очищення кешу Expo
expo start --clear
```

### Вирішення поширених проблем
Детальні інструкції з вирішення проблем з Docker знаходяться в файлі [`docs/DOCKER_TROUBLESHOOTING.md`](./docs/DOCKER_TROUBLESHOOTING.md).

---
*Цей документ замінює собою численні застарілі інструкції та є єдиним джерелом правди для швидкого старту.*