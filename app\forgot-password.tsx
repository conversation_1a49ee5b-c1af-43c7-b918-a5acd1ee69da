import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { ActivityIndicator, View, ScrollView, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import * as z from "zod";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormInput } from "@/components/ui/form";
import { Text } from "@/components/ui/text";
import { H1, H2, Muted } from "@/components/ui/typography";
import { useAuth } from "@/context/supabase-provider";
import { useColorScheme } from "@/lib/useColorScheme";

const formSchema = z.object({
  email: z.string().email("Будь ласка, введіть дійсну email адресу."),
});

export default function ForgotPassword() {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const { resetPassword } = useAuth();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(data: z.infer<typeof formSchema>) {
    setError(null);
    try {
      await resetPassword(data.email);
      setIsSubmitted(true);
    } catch (error: Error | any) {
      console.error(error.message);
      setError(error.message || "Помилка відправки. Спробуйте ще раз.");
    }
  }

  if (isSubmitted) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 p-4 justify-center items-center">
          <View className="bg-primary/10 rounded-full p-4 mb-6">
            <Ionicons name="mail-outline" size={48} color="#f97316" />
          </View>
          
          <H1 className="text-center mb-4">Перевірте пошту!</H1>
          
          <Muted className="text-center mb-8 max-w-sm">
            Ми відправили інструкції для відновлення паролю на вашу email адресу.
          </Muted>
          
          <Button
            size="lg"
            variant="default"
            onPress={() => router.push("/sign-in")}
            className="w-full max-w-sm"
          >
            <Text>Повернутися до входу</Text>
          </Button>
          
          <Pressable 
            onPress={() => setIsSubmitted(false)}
            className="mt-4"
          >
            <Text className="text-primary">Не отримали лист? Спробувати ще раз</Text>
          </Pressable>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-border">
          <Pressable onPress={() => router.back()}>
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={colorScheme === "dark" ? "#fff" : "#000"} 
            />
          </Pressable>
          <H2>Відновлення паролю</H2>
          <View style={{ width: 24 }} />
        </View>

        {/* Form */}
        <View className="p-4">
          <View className="items-center mb-8">
            <View className="bg-primary/10 rounded-full p-4 mb-4">
              <Ionicons name="lock-closed-outline" size={32} color="#f97316" />
            </View>
            <H1 className="text-center mb-2">Забули пароль?</H1>
            <Muted className="text-center max-w-sm">
              Не хвилюйтеся! Введіть email, який ви використовували при реєстрації, і ми надішлемо інструкції для відновлення.
            </Muted>
          </View>

          {/* Error Message */}
          {error && (
            <View className="bg-destructive/10 border border-destructive/20 rounded-lg p-3 mb-4">
              <Text className="text-destructive text-sm">{error}</Text>
            </View>
          )}

          <Form {...form}>
            <View className="gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormInput
                    label="Email адреса"
                    placeholder="<EMAIL>"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    keyboardType="email-address"
                    {...field}
                  />
                )}
              />
            </View>
          </Form>

          {/* Submit Button */}
          <Button
            size="lg"
            variant="default"
            onPress={form.handleSubmit(onSubmit)}
            disabled={form.formState.isSubmitting}
            className="w-full mt-6"
          >
            {form.formState.isSubmitting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text>Відправити інструкції</Text>
            )}
          </Button>

          {/* Back to Sign In */}
          <View className="flex-row items-center justify-center gap-x-2 mt-8">
            <Muted>Згадали пароль?</Muted>
            <Pressable onPress={() => router.push("/sign-in")}>
              <Text className="text-primary font-medium">Увійти</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}