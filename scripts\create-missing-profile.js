/**
 * Скрипт для створення відсутнього профілю користувача
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const TARGET_EMAIL = '<EMAIL>';
const USER_ID = 'f15eb141-46b2-4b87-b5f0-5542f60b27a5';

console.log('👤 СТВОРЕННЯ ВІДСУТНЬОГО ПРОФІЛЮ');
console.log('=' .repeat(40));

async function createProfileDirectly() {
    console.log(`📧 Створення профілю для: ${TARGET_EMAIL}`);
    console.log(`🆔 User ID: ${USER_ID}`);
    
    try {
        // Використовуємо прямий SQL запит для створення профілю
        const { data, error } = await supabaseAdmin.rpc('create_user_profile', {
            user_id: USER_ID,
            user_email: TARGET_EMAIL,
            user_name: 'Public Relations'
        });
        
        if (error) {
            console.log('⚠️ RPC функція не знайдена, використовуємо прямий INSERT...');
            
            // Прямий INSERT через SQL
            const { data: insertData, error: insertError } = await supabaseAdmin
                .from('profiles')
                .insert({
                    id: USER_ID,
                    email: TARGET_EMAIL,
                    name: 'Public Relations',
                    subscription_type: 'free'
                })
                .select();
                
            if (insertError) {
                console.log('❌ Помилка створення профілю:', insertError.message);
                
                // Спробуємо через raw SQL
                console.log('🔧 Спроба через raw SQL...');
                const { data: sqlData, error: sqlError } = await supabaseAdmin
                    .rpc('exec_sql', {
                        sql: `INSERT INTO profiles (id, email, name, subscription_type) 
                              VALUES ('${USER_ID}', '${TARGET_EMAIL}', 'Public Relations', 'free')
                              ON CONFLICT (id) DO UPDATE SET 
                                email = EXCLUDED.email,
                                name = EXCLUDED.name,
                                updated_at = NOW()`
                    });
                    
                if (sqlError) {
                    console.log('❌ SQL помилка:', sqlError.message);
                    return false;
                }
                
                console.log('✅ Профіль створений через SQL');
                return true;
            }
            
            console.log('✅ Профіль створений через INSERT');
            console.log('📄 Дані профілю:', insertData);
            return true;
        }
        
        console.log('✅ Профіль створений через RPC');
        return true;
        
    } catch (error) {
        console.log('❌ Критична помилка:', error.message);
        return false;
    }
}

async function verifyProfile() {
    console.log('\n🔍 Перевірка створеного профілю...');
    
    try {
        const { data, error } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .eq('id', USER_ID)
            .single();
            
        if (error) {
            console.log('❌ Профіль не знайдений:', error.message);
            return false;
        }
        
        console.log('✅ Профіль знайдений:');
        console.log(`   📧 Email: ${data.email}`);
        console.log(`   👤 Ім'я: ${data.name}`);
        console.log(`   💳 Підписка: ${data.subscription_type}`);
        console.log(`   📅 Створено: ${data.created_at}`);
        
        return true;
        
    } catch (error) {
        console.log('❌ Помилка перевірки:', error.message);
        return false;
    }
}

async function testAuthentication() {
    console.log('\n🔐 Тестування автентифікації з тимчасовим паролем...');
    
    // Створюємо клієнт для тестування
    const testClient = createClient(
        process.env.EXPO_PUBLIC_SUPABASE_URL,
        process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
    );
    
    try {
        const { data, error } = await testClient.auth.signInWithPassword({
            email: TARGET_EMAIL,
            password: 'TempPass123!'
        });
        
        if (error) {
            console.log('❌ Помилка автентифікації:', error.message);
            return false;
        }
        
        console.log('✅ Автентифікація успішна!');
        console.log(`👤 Користувач: ${data.user.email}`);
        console.log(`🆔 ID: ${data.user.id}`);
        
        // Перевіряємо доступ до профілю
        const { data: profile, error: profileError } = await testClient
            .from('profiles')
            .select('*')
            .eq('id', data.user.id)
            .single();
            
        if (profileError) {
            console.log('⚠️ Не вдалося отримати профіль:', profileError.message);
        } else {
            console.log('✅ Профіль доступний через RLS');
        }
        
        // Вихід з тестового сеансу
        await testClient.auth.signOut();
        
        return true;
        
    } catch (error) {
        console.log('❌ Помилка тестування:', error.message);
        return false;
    }
}

async function main() {
    try {
        // Створюємо профіль
        const profileCreated = await createProfileDirectly();
        
        if (!profileCreated) {
            console.log('\n❌ Не вдалося створити профіль');
            return;
        }
        
        // Перевіряємо профіль
        const profileExists = await verifyProfile();
        
        if (!profileExists) {
            console.log('\n❌ Профіль не створився');
            return;
        }
        
        // Тестуємо автентифікацію
        const authWorks = await testAuthentication();
        
        console.log('\n' + '='.repeat(40));
        
        if (authWorks) {
            console.log('🎉 ПРОБЛЕМУ ВИРІШЕНО!');
            console.log('\n📋 РЕЗУЛЬТАТ:');
            console.log('✅ Профіль створений');
            console.log('✅ Автентифікація працює');
            console.log('✅ RLS політики працюють');
            console.log('\n🔐 Користувач може увійти з паролем: TempPass123!');
            console.log('⚠️ Попросіть користувача змінити пароль після входу');
        } else {
            console.log('⚠️ ПРОФІЛЬ СТВОРЕНИЙ, АЛЕ ПОТРІБНА ДОДАТКОВА ДІАГНОСТИКА');
            console.log('\n📋 НАСТУПНІ КРОКИ:');
            console.log('1. Перевірте RLS політики');
            console.log('2. Запустіть повну діагностику: node scripts/diagnose-auth-issue.js');
        }
        
    } catch (error) {
        console.log('\n❌ КРИТИЧНА ПОМИЛКА:', error.message);
    }
}

main();