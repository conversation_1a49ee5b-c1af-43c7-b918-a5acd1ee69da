import React from "react";
import { Pressable, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { cn } from "@/lib/utils";
import { useColorScheme } from "@/lib/useColorScheme";

interface CheckboxProps {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function Checkbox({
  checked = false,
  onCheckedChange,
  disabled = false,
  className,
}: CheckboxProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <Pressable
      onPress={() => !disabled && onCheckedChange?.(!checked)}
      disabled={disabled}
      className={cn(
        "h-5 w-5 rounded border-2 items-center justify-center",
        checked
          ? "bg-primary border-primary"
          : isDark
          ? "bg-gray-800 border-gray-600"
          : "bg-white border-gray-300",
        disabled && "opacity-50",
        className
      )}
    >
      {checked && (
        <Ionicons
          name="checkmark"
          size={14}
          color="#fff"
        />
      )}
    </Pressable>
  );
}