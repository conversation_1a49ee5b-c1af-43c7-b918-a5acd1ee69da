import React, { useState } from "react";
import { View, ScrollView, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/context/supabase-provider";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { H1, Muted } from "@/components/ui/typography";
import { useColorScheme } from "@/lib/useColorScheme";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

const subscriptionPlans = [
  {
    key: "yearly",
    title: "РІЧНИЙ",
    price: "799",
    priceSuffix: "грн/рік",
    highlight: "Економія 33%",
    features: [
      "Всі калькулятори",
      "Необмежені проекти",
      "Експорт у PDF",
      "Преміум підтримка",
    ],
  },
  {
    key: "monthly",
    title: "МІСЯЧНИЙ",
    price: "99",
    priceSuffix: "грн/місяць",
    features: [
        "Всі калькулятори", 
        "Повний доступ",
        "Базова підтримка"
    ],
  },
];

export default function SubscriptionPlansScreen() {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const { session } = useAuth();
  const [selectedPlan, setSelectedPlan] = useState("yearly");
  const [promoCode, setPromoCode] = useState("");

  const handleSelectPlan = (planKey: string) => {
    setSelectedPlan(planKey);
  };

  const handleContinue = () => {
    const plan = subscriptionPlans.find((p) => p.key === selectedPlan);
    if (plan) {
      router.push({
        pathname: "/payment",
        params: { plan: plan.key, price: plan.price },
      });
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-row items-center justify-between p-4">
        <Pressable onPress={() => router.back()}>
          <Ionicons
            name="close"
            size={24}
            color={colorScheme === "dark" ? "#fff" : "#000"}
          />
        </Pressable>
        <H1 className="text-lg">Обрати план</H1>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView className="flex-1 px-4 py-6">
        <View className="gap-y-6">
          {subscriptionPlans.map((plan) => (
            <Pressable key={plan.key} onPress={() => handleSelectPlan(plan.key)}>
              <Card 
                className={`
                  ${selectedPlan === plan.key ? "border-primary" : "border-border"}
                  ${plan.highlight ? "bg-primary/5" : "bg-card"}
                `}
              >
                <CardHeader>
                  <View className="flex-row justify-between items-center">
                    <CardTitle className="text-xl">{plan.title}</CardTitle>
                    {plan.highlight && (
                      <View className="bg-primary rounded-full px-3 py-1">
                        <Text className="text-primary-foreground text-xs font-semibold">
                          {plan.highlight}
                        </Text>
                      </View>
                    )}
                  </View>
                  <View className="flex-row items-baseline gap-x-1">
                    <H1 className="text-4xl">{plan.price}</H1>
                    <Text className="text-muted-foreground">{plan.priceSuffix}</Text>
                  </View>
                </CardHeader>
                <CardContent>
                  <View className="gap-y-2">
                    {plan.features.map((feature, index) => (
                      <View key={index} className="flex-row items-center gap-x-2">
                        <Ionicons name="checkmark-circle" size={16} className="text-green-500" />
                        <Text>{feature}</Text>
                      </View>
                    ))}
                  </View>
                </CardContent>
                <CardFooter>
                    <View 
                        className={`w-6 h-6 rounded-full border-2 flex items-center justify-center
                            ${selectedPlan === plan.key ? "border-primary bg-primary" : "border-muted-foreground"}`
                        }
                    >
                        {selectedPlan === plan.key && <Ionicons name="checkmark" size={16} color="white" />}
                    </View>
                    <Text className="ml-3 font-semibold">
                        {selectedPlan === plan.key ? "Обрано" : "Обрати"}
                    </Text>
                </CardFooter>
              </Card>
            </Pressable>
          ))}
        </View>

        <View className="mt-8">
            <Text className="text-center text-muted-foreground mb-2">Маєте промокод?</Text>
            <View className="flex-row gap-x-2">
                <Input 
                    placeholder="Ввести промокод" 
                    value={promoCode}
                    onChangeText={setPromoCode}
                    className="flex-1"
                />
                <Button variant="outline" onPress={() => { /* TODO: Apply promo code */ }}>
                    <Text>Застосувати</Text>
                </Button>
            </View>
        </View>
      </ScrollView>

      <View className="px-6 pb-6 border-t border-border pt-4">
        <Button size="lg" onPress={handleContinue}>
          <Text>Продовжити</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}