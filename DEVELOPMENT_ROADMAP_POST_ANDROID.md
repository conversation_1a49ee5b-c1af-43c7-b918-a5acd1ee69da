# План розробки після вирішення Android емулятора

## 🎯 Поточний статус (23.06.2025)
**КРИТИЧНЕ ДОСЯГНЕННЯ**: Android емулятор повністю функціональний
- ✅ Додаток збирається та запускається на емуляторі (5780ms, 1497 модулів)
- ✅ Автентифікація працює (Supabase Auth + Google OAuth)
- ✅ Навігація функціонує коректно
- ✅ Supabase інтеграція активна
- ✅ Готовність до розробки функцій

## 🚀 Детальний план розробки

### Фаза 1: Основні екрани та навігація (Тиждень 1-2)
**Мета**: Створити базову структуру додатку з головними екранами

#### 1.1 Головний екран (Dashboard)
**Файли для створення**:
- `app/screens/HomeScreen.tsx`
- `components/dashboard/QuickActions.tsx`
- `components/dashboard/RecentProjects.tsx`
- `components/dashboard/UserStats.tsx`

**Функціональність**:
- Швидкий доступ до калькуляторів
- Останні 3 проєкти
- Статистика користувача (кількість проєктів, розрахунків)
- Нотифікації та оновлення

**UI компоненти**:
```typescript
interface DashboardProps {
  user: User
  recentProjects: Project[]
  stats: UserStats
}

const HomeScreen: React.FC = () => {
  const { user } = useAuth()
  const { projects } = useProjects(user.id, { limit: 3 })
  const { stats } = useUserStats(user.id)
  
  return (
    <ScrollView className="flex-1 bg-gray-50">
      <QuickActions />
      <RecentProjects projects={projects} />
      <UserStats stats={stats} />
    </ScrollView>
  )
}
```

#### 1.2 Екран калькуляторів
**Файли для створення**:
- `app/screens/calculators/CalculatorsScreen.tsx`
- `components/calculators/CalculatorCard.tsx`
- `components/calculators/CalculatorCategories.tsx`

**Функціональність**:
- Список доступних калькуляторів
- Категорії (пряжа, спиці, патерни)
- Історія розрахунків
- Збережені шаблони

#### 1.3 Екран проєктів
**Файли для створення**:
- `app/screens/projects/ProjectsScreen.tsx`
- `app/screens/projects/ProjectDetailsScreen.tsx`
- `components/projects/ProjectCard.tsx`
- `components/projects/ProjectFilters.tsx`

**Функціональність**:
- Список активних проєктів
- Фільтрація за статусом
- Швидкі дії (лічильник рядів)
- Прогрес проєктів

### Фаза 2: Перший функціональний калькулятор (Тиждень 3)
**Мета**: Реалізувати повний цикл роботи з калькулятором пряжі

#### 2.1 UI форма калькулятора
**Файл**: `app/screens/calculators/YarnCalculatorScreen.tsx`

```typescript
interface YarnCalculatorForm {
  garmentType: 'sweater' | 'hat' | 'scarf' | 'socks'
  size: string
  gauge: {
    stitches: number
    rows: number
    inches: number
  }
  yarnWeight: 'lace' | 'dk' | 'worsted' | 'chunky'
}

const YarnCalculatorScreen: React.FC = () => {
  const [form, setForm] = useState<YarnCalculatorForm>()
  const { calculate, result, loading } = useYarnCalculator()
  
  const handleCalculate = async () => {
    const result = await calculate(form)
    // Показати результат
    // Запропонувати створити проєкт
  }
  
  return (
    <ScrollView className="flex-1 p-4">
      <GarmentTypeSelector />
      <SizeSelector />
      <GaugeInput />
      <YarnWeightSelector />
      <CalculateButton onPress={handleCalculate} />
      {result && <ResultDisplay result={result} />}
    </ScrollView>
  )
}
```

#### 2.2 Edge Function для розрахунків
**Файл**: `supabase/functions/yarn-calculator/index.ts`

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"

interface YarnCalculationRequest {
  garmentType: 'sweater' | 'hat' | 'scarf' | 'socks'
  size: string
  gauge: {
    stitches: number
    rows: number
    inches: number
  }
  yarnWeight: 'lace' | 'dk' | 'worsted' | 'chunky'
}

interface YarnCalculationResult {
  totalYardage: number
  totalWeight: number
  skeinsNeeded: number
  confidence: number
  recommendations: string[]
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const params: YarnCalculationRequest = await req.json()
    
    // Валідація вхідних даних
    if (!params.garmentType || !params.size || !params.gauge || !params.yarnWeight) {
      throw new Error('Missing required parameters')
    }
    
    // Розрахунок кількості пряжі
    const result = calculateYarnAmount(params)
    
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

function calculateYarnAmount(params: YarnCalculationRequest): YarnCalculationResult {
  // Базові розрахунки для різних типів виробів
  const garmentMultipliers = {
    sweater: { base: 1200, sizeMultiplier: 1.2 },
    hat: { base: 200, sizeMultiplier: 1.1 },
    scarf: { base: 400, sizeMultiplier: 1.0 },
    socks: { base: 300, sizeMultiplier: 1.15 }
  }
  
  const sizeMultipliers = {
    'XS': 0.8, 'S': 0.9, 'M': 1.0, 'L': 1.1, 'XL': 1.2, 'XXL': 1.3
  }
  
  const weightMultipliers = {
    'lace': 0.7, 'dk': 0.9, 'worsted': 1.0, 'chunky': 1.3
  }
  
  const garment = garmentMultipliers[params.garmentType]
  const sizeMultiplier = sizeMultipliers[params.size] || 1.0
  const weightMultiplier = weightMultipliers[params.yarnWeight]
  
  // Розрахунок базової кількості
  const baseYardage = garment.base * sizeMultiplier * weightMultiplier
  
  // Корекція на щільність в'язання
  const gaugeAdjustment = (params.gauge.stitches / 20) * (params.gauge.rows / 28)
  const adjustedYardage = baseYardage * gaugeAdjustment
  
  // Додаємо 10% запас
  const totalYardage = Math.ceil(adjustedYardage * 1.1)
  
  // Розрахунок ваги (приблизно)
  const yardagePerOunce = { lace: 400, dk: 200, worsted: 150, chunky: 100 }
  const totalWeight = totalYardage / yardagePerOunce[params.yarnWeight]
  
  // Кількість мотків (припускаємо 100г на моток)
  const skeinsNeeded = Math.ceil(totalWeight / 3.5) // 3.5 oz = 100g
  
  return {
    totalYardage,
    totalWeight,
    skeinsNeeded,
    confidence: 0.85,
    recommendations: [
      'Купіть на один моток більше для запасу',
      'Перевірте щільність в\'язання на зразку',
      'Врахуйте усадку після прання'
    ]
  }
}
```

#### 2.3 Офлайн-логіка
**Файл**: `lib/calculations/yarnCalculator.ts`

```typescript
export const calculateYarnOffline = (params: YarnCalculationRequest): YarnCalculationResult => {
  // Ідентична логіка до Edge Function для офлайн-режиму
  return calculateYarnAmount(params)
}

export const useYarnCalculator = () => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<YarnCalculationResult | null>(null)
  
  const calculate = async (params: YarnCalculationRequest) => {
    setLoading(true)
    
    try {
      // Спочатку спробувати онлайн
      const { data, error } = await supabase.functions.invoke('yarn-calculator', {
        body: params
      })
      
      if (error) throw error
      
      // Зберегти результат в WatermelonDB
      await saveCalculation('yarn', params, data)
      
      setResult(data)
      return data
      
    } catch (error) {
      console.log('Online calculation failed, using offline:', error)
      
      // Fallback на офлайн-розрахунки
      const offlineResult = calculateYarnOffline(params)
      
      // Зберегти результат локально
      await saveCalculation('yarn', params, offlineResult)
      
      setResult(offlineResult)
      return offlineResult
      
    } finally {
      setLoading(false)
    }
  }
  
  return { calculate, result, loading }
}
```

#### 2.4 WatermelonDB інтеграція
**Файл**: `database/models/Calculation.ts`

```typescript
import { Model } from '@nozbe/watermelondb'
import { text, json, date, relation } from '@nozbe/watermelondb/decorators'

export class Calculation extends Model {
  static table = 'calculations'
  static associations = {
    projects: { type: 'belongs_to', key: 'project_id' },
    users: { type: 'belongs_to', key: 'user_id' }
  }
  
  @text('type') type!: string // 'yarn', 'needle', 'gauge'
  @json('input_data', sanitizeInputData) inputData!: any
  @json('result_data', sanitizeResultData) resultData!: any
  @text('project_id') projectId?: string
  @text('user_id') userId!: string
  @date('created_at') createdAt!: Date
  @date('synced_at') syncedAt!: Date
  
  @relation('projects', 'project_id') project?: Relation<Project>
  @relation('users', 'user_id') user!: Relation<User>
}

const sanitizeInputData = (data: any) => data
const sanitizeResultData = (data: any) => data

// Функція збереження розрахунку
export const saveCalculation = async (
  type: string,
  inputData: any,
  resultData: any,
  projectId?: string
) => {
  return await database.write(async () => {
    const calculation = await database.get<Calculation>('calculations').create(calc => {
      calc.type = type
      calc.inputData = inputData
      calc.resultData = resultData
      calc.projectId = projectId
      calc.userId = getCurrentUserId()
      calc.createdAt = new Date()
      calc.syncedAt = new Date()
    })
    
    return calculation
  })
}
```

### Фаза 3: WatermelonDB синхронізація (Тиждень 4)
**Мета**: Налаштувати повну синхронізацію між локальною та серверною базами

#### 3.1 Sync Adapter
**Файл**: `database/sync.ts`

```typescript
import { synchronize } from '@nozbe/watermelondb/sync'
import { database } from './index'
import { supabase } from '../lib/supabase'

export const syncDatabase = async () => {
  await synchronize({
    database,
    pullChanges: async ({ lastPulledAt }) => {
      const { data, error } = await supabase.rpc('pull_changes', {
        last_pulled_at: lastPulledAt ? new Date(lastPulledAt).toISOString() : null
      })
      
      if (error) throw error
      
      return {
        changes: data.changes,
        timestamp: Date.now()
      }
    },
    pushChanges: async ({ changes }) => {
      const { error } = await supabase.rpc('push_changes', {
        changes: changes
      })
      
      if (error) throw error
    }
  })
}
```

#### 3.2 Supabase RPC Functions
**Файл**: `supabase/migrations/003_sync_functions.sql`

```sql
-- Pull Changes Function
CREATE OR REPLACE FUNCTION pull_changes(last_pulled_at TIMESTAMP WITH TIME ZONE)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'changes', json_build_object(
      'calculations', (
        SELECT COALESCE(json_agg(row_to_json(calculations)), '[]'::json)
        FROM calculations
        WHERE updated_at > COALESCE(last_pulled_at, '1970-01-01'::timestamp)
        AND user_id = auth.uid()
      ),
      'projects', (
        SELECT COALESCE(json_agg(row_to_json(projects)), '[]'::json)
        FROM projects
        WHERE updated_at > COALESCE(last_pulled_at, '1970-01-01'::timestamp)
        AND user_id = auth.uid()
      )
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Push Changes Function
CREATE OR REPLACE FUNCTION push_changes(changes JSON)
RETURNS VOID AS $$
DECLARE
  change_record RECORD;
BEGIN
  -- Process calculations
  FOR change_record IN 
    SELECT * FROM json_populate_recordset(null::calculations, changes->'calculations')
  LOOP
    INSERT INTO calculations (id, type, input_data, result_data, project_id, user_id, created_at, updated_at)
    VALUES (
      change_record.id,
      change_record.type,
      change_record.input_data,
      change_record.result_data,
      change_record.project_id,
      auth.uid(),
      change_record.created_at,
      change_record.updated_at
    )
    ON CONFLICT (id) DO UPDATE SET
      type = EXCLUDED.type,
      input_data = EXCLUDED.input_data,
      result_data = EXCLUDED.result_data,
      updated_at = EXCLUDED.updated_at;
  END LOOP;
  
  -- Process projects
  FOR change_record IN 
    SELECT * FROM json_populate_recordset(null::projects, changes->'projects')
  LOOP
    INSERT INTO projects (id, title, description, pattern_data, user_id, created_at, updated_at)
    VALUES (
      change_record.id,
      change_record.title,
      change_record.description,
      change_record.pattern_data,
      auth.uid(),
      change_record.created_at,
      change_record.updated_at
    )
    ON CONFLICT (id) DO UPDATE SET
      title = EXCLUDED.title,
      description = EXCLUDED.description,
      pattern_data = EXCLUDED.pattern_data,
      updated_at = EXCLUDED.updated_at;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 📊 Архітектурна діаграма після Android налаштування

```mermaid
graph TB
    subgraph "Mobile App (Android Ready ✅)"
        UI[React Native UI]
        NAV[React Navigation]
        AUTH[Supabase Auth]
        WDB[(WatermelonDB)]
        SYNC[Sync Engine]
        CALC[Calculators]
    end
    
    subgraph "Supabase Backend"
        PG[(PostgreSQL)]
        EF[Edge Functions]
        RT[Realtime]
        RPC[RPC Functions]
    end
    
    subgraph "Development Environment"
        EMULATOR[Android Emulator ✅]
        EXPO[Expo Dev Server]
        ADB[ADB Bridge ✅]
    end
    
    UI --> NAV
    NAV --> AUTH
    AUTH --> WDB
    WDB --> SYNC
    SYNC <--> RPC
    RPC <--> PG
    CALC --> EF
    RT --> UI
    
    EXPO --> EMULATOR
    EMULATOR --> ADB
    ADB --> UI
    
    style EMULATOR fill:#90EE90
    style UI fill:#87CEEB
    style WDB fill:#FFB6C1
    style CALC fill:#FFD700
```

## 🔧 Команди для розробки

### Запуск та тестування
```bash
# Основні команди
yarn android          # Збірка та запуск на емуляторі
yarn start            # Expo dev server
yarn test             # Unit тести
yarn lint             # Перевірка коду

# Supabase команди
npx supabase start    # Локальний Supabase
npx supabase db push  # Застосувати міграції
npx supabase functions deploy yarn-calculator  # Деплой функції

# Debugging
npx react-devtools   # React DevTools
adb logcat           # Android логи
```

### Структура файлів для розробки
```
app/
├── screens/
│   ├── HomeScreen.tsx                    # Фаза 1
│   ├── calculators/
│   │   ├── CalculatorsScreen.tsx         # Фаза 1
│   │   └── YarnCalculatorScreen.tsx      # Фаза 2
│   └── projects/
│       ├── ProjectsScreen.tsx            # Фаза 1
│       └── ProjectDetailsScreen.tsx      # Фаза 1
├── components/
│   ├── dashboard/                        # Фаза 1
│   ├── calculators/                      # Фаза 2
│   └── projects/                         # Фаза 1
└── hooks/
    ├── useYarnCalculator.ts              # Фаза 2
    └── useProjects.ts                    # Фаза 1

database/
├── models/
│   ├── Calculation.ts                    # Фаза 2
│   └── Project.ts                        # Фаза 1
└── sync.ts                               # Фаза 3

supabase/
├── functions/
│   └── yarn-calculator/                  # Фаза 2
└── migrations/
    └── 003_sync_functions.sql            # Фаза 3
```

## 📈 Метрики успіху

### Фаза 1 (Екрани):
- ✅ Всі основні екрани створені та працюють на Android
- ✅ Навігація між екранами функціонує
- ✅ UI відповідає дизайн-системі
- ✅ Responsive layout для різних розмірів екрану

### Фаза 2 (Калькулятор):
- ✅ Форма валідації працює коректно
- ✅ Розрахунки точні (тестування з еталонними значеннями)
- ✅ Офлайн-режим функціонує без помилок
- ✅ Результати зберігаються в WatermelonDB

### Фаза 3 (Синхронізація):
- ✅ Дані синхронізуються між пристроями
- ✅ Конфлікти вирішуються коректно
- ✅ Офлайн-зміни не втрачаються при відновленні з'єднання
- ✅ Performance залишається оптимальним

## 🎯 Готовність до впровадження

**Поточний стан**: ✅ Android платформа повністю готова до розробки функцій
**Наступний крок**: Розпочати Фазу 1 - створення основних екранів
**Очікуваний результат**: Функціональний MVP через 4 тижні
**Ключове досягнення**: Вирішення проблеми з емулятором дозволяє сконцентруватися на функціональності

---

*Створено: 23.06.2025*  
*Базується на: ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md*  
*Статус: Готово до впровадження*