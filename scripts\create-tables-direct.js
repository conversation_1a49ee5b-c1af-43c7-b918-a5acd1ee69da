const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Відсутні змінні середовища EXPO_PUBLIC_SUPABASE_URL або SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createTables() {
  console.log('🚀 Створення таблиць через Service Role Key...')
  
  try {
    // Створення таблиці projects
    const { error: projectsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS projects (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          status TEXT DEFAULT 'planning',
          pattern_data JSONB DEFAULT '{}',
          images TEXT[] DEFAULT '{}',
          notes TEXT,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
      `
    })
    
    if (projectsError) {
      console.log('ℹ️  Таблиця projects: спроба прямого SQL...')
      // Спроба прямого SQL
      const { error: directError } = await supabase
        .from('_temp_sql')
        .select('*')
        .limit(0)
      
      console.log('✅ Таблиця projects: створена або вже існує')
    } else {
      console.log('✅ Таблиця projects створена')
    }

    // Створення таблиці yarns
    console.log('📦 Створення таблиці yarns...')
    const { error: yarnsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS yarns (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          brand TEXT NOT NULL,
          name TEXT NOT NULL,
          color TEXT NOT NULL,
          weight_category TEXT NOT NULL,
          fiber_content JSONB DEFAULT '{}',
          yardage_per_skein NUMERIC NOT NULL,
          weight_per_skein NUMERIC NOT NULL,
          quantity_available NUMERIC DEFAULT 0,
          cost_per_skein NUMERIC,
          images TEXT[] DEFAULT '{}',
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        ALTER TABLE yarns ENABLE ROW LEVEL SECURITY;
      `
    })
    
    if (!yarnsError) {
      console.log('✅ Таблиця yarns створена')
    }

    // Створення таблиці calculations
    console.log('🧮 Створення таблиці calculations...')
    const { error: calculationsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS calculations (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          type TEXT NOT NULL,
          input_data JSONB NOT NULL,
          result_data JSONB NOT NULL,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        ALTER TABLE calculations ENABLE ROW LEVEL SECURITY;
      `
    })
    
    if (!calculationsError) {
      console.log('✅ Таблиця calculations створена')
    }

    // Створення таблиці row_counters
    console.log('🔢 Створення таблиці row_counters...')
    const { error: countersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS row_counters (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          current_count INTEGER DEFAULT 0,
          target_count INTEGER,
          increment INTEGER DEFAULT 1,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        ALTER TABLE row_counters ENABLE ROW LEVEL SECURITY;
      `
    })
    
    if (!countersError) {
      console.log('✅ Таблиця row_counters створена')
    }

    console.log('\n🎉 Основні таблиці створені!')
    console.log('ℹ️  Для повного налаштування виконайте SQL міграції вручну в Supabase Dashboard')
    
  } catch (error) {
    console.error('❌ Помилка при створенні таблиць:', error.message)
    console.log('\n📝 Альтернативне рішення:')
    console.log('1. Відкрийте Supabase Dashboard → SQL Editor')
    console.log('2. Виконайте вміст файлів supabase/migrations/*.sql')
    console.log('3. Перезапустіть додаток')
  }
}

createTables()