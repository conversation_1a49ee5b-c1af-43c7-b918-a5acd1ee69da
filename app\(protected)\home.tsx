import React from 'react'
import { View, ScrollView, Text, Pressable } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useColorScheme } from 'nativewind'
import { colors } from '../../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'

export default function HomeScreen() {
  const { colorScheme } = useColorScheme()
  const isDark = colorScheme === 'dark'

  const quickActions = [
    {
      id: 'yarn-calculator',
      title: 'Калькулятор пряжі',
      description: 'Розрахуйте кількість пряжі для проєкту',
      icon: 'calculator-outline' as const,
      route: '/calculators/yarn',
      color: isDark ? colors.dark.primary : colors.light.primary
    },
    {
      id: 'new-project',
      title: 'Новий проєкт',
      description: 'Створіть новий проєкт в\'язання',
      icon: 'add-circle-outline' as const,
      route: '/projects/new',
      color: isDark ? colors.dark.accent : colors.light.accent
    },
    {
      id: 'yarn-stash',
      title: 'Моя пряжа',
      description: 'Переглянути запаси пряжі',
      icon: 'library-outline' as const,
      route: '/yarn',
      color: isDark ? colors.dark.secondary : colors.light.secondary
    },
    {
      id: 'community',
      title: 'Спільнота',
      description: 'Поділіться своїми роботами',
      icon: 'people-outline' as const,
      route: '/community',
      color: isDark ? colors.dark.muted : colors.light.muted
    }
  ]

  const recentProjects = [
    {
      id: '1',
      title: 'Светр з косами',
      progress: 65,
      lastWorked: '2 дні тому'
    },
    {
      id: '2',
      title: 'Дитяча шапочка',
      progress: 90,
      lastWorked: 'Вчора'
    }
  ]

  return (
    <SafeAreaView 
      style={{ 
        flex: 1, 
        backgroundColor: isDark ? colors.dark.background : colors.light.background 
      }}
    >
      <ScrollView className="flex-1 px-4">
        {/* Header */}
        <View className="py-6">
          <Text 
            className="text-3xl font-bold mb-2"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            Вітаємо! 👋
          </Text>
          <Text 
            className="text-base"
            style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
          >
            Готові до нового проєкту в'язання?
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text 
            className="text-xl font-semibold mb-4"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            Швидкі дії
          </Text>
          <View className="flex-row flex-wrap gap-3">
            {quickActions.map((action) => (
              <Pressable
                key={action.id}
                onPress={() => router.push(action.route as any)}
                className="flex-1 min-w-[45%] p-4 rounded-xl"
                style={{ 
                  backgroundColor: isDark ? colors.dark.card : colors.light.card,
                  borderColor: isDark ? colors.dark.border : colors.light.border,
                  borderWidth: 1
                }}
              >
                <View className="items-center">
                  <Ionicons 
                    name={action.icon} 
                    size={32} 
                    color={action.color}
                    style={{ marginBottom: 8 }}
                  />
                  <Text 
                    className="text-sm font-medium text-center mb-1"
                    style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                  >
                    {action.title}
                  </Text>
                  <Text 
                    className="text-xs text-center"
                    style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                  >
                    {action.description}
                  </Text>
                </View>
              </Pressable>
            ))}
          </View>
        </View>

        {/* Recent Projects */}
        <View className="mb-8">
          <View className="flex-row justify-between items-center mb-4">
            <Text 
              className="text-xl font-semibold"
              style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
            >
              Останні проєкти
            </Text>
            <Pressable onPress={() => router.push('/projects')}>
              <Text 
                className="text-sm font-medium"
                style={{ color: isDark ? colors.dark.primary : colors.light.primary }}
              >
                Всі проєкти
              </Text>
            </Pressable>
          </View>
          
          {recentProjects.length > 0 ? (
            <View className="gap-3">
              {recentProjects.map((project) => (
                <Pressable
                  key={project.id}
                  className="p-4 rounded-xl"
                  style={{ 
                    backgroundColor: isDark ? colors.dark.card : colors.light.card,
                    borderColor: isDark ? colors.dark.border : colors.light.border,
                    borderWidth: 1
                  }}
                >
                  <View className="flex-row justify-between items-start mb-2">
                    <Text 
                      className="text-base font-medium flex-1"
                      style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                    >
                      {project.title}
                    </Text>
                    <Text 
                      className="text-sm ml-2"
                      style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                    >
                      {project.progress}%
                    </Text>
                  </View>
                  
                  {/* Progress Bar */}
                  <View 
                    className="h-2 rounded-full mb-2"
                    style={{ backgroundColor: isDark ? colors.dark.muted : colors.light.muted }}
                  >
                    <View 
                      className="h-full rounded-full"
                      style={{ 
                        width: `${project.progress}%`,
                        backgroundColor: isDark ? colors.dark.primary : colors.light.primary
                      }}
                    />
                  </View>
                  
                  <Text 
                    className="text-xs"
                    style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                  >
                    Останній раз: {project.lastWorked}
                  </Text>
                </Pressable>
              ))}
            </View>
          ) : (
            <View 
              className="p-8 rounded-xl items-center"
              style={{ 
                backgroundColor: isDark ? colors.dark.card : colors.light.card,
                borderColor: isDark ? colors.dark.border : colors.light.border,
                borderWidth: 1
              }}
            >
              <Ionicons 
                name="folder-open-outline" 
                size={48} 
                color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                style={{ marginBottom: 12 }}
              />
              <Text 
                className="text-base font-medium mb-2"
                style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
              >
                Поки що немає проєктів
              </Text>
              <Text 
                className="text-sm text-center mb-4"
                style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
              >
                Створіть свій перший проєкт в'язання
              </Text>
              <Pressable
                onPress={() => router.push('/projects/new')}
                className="px-6 py-3 rounded-full"
                style={{ backgroundColor: isDark ? colors.dark.primary : colors.light.primary }}
              >
                <Text 
                  className="text-sm font-medium"
                  style={{ color: isDark ? colors.dark.primaryForeground : colors.light.primaryForeground }}
                >
                  Створити проєкт
                </Text>
              </Pressable>
            </View>
          )}
        </View>

        {/* Tips Section */}
        <View className="mb-8">
          <Text 
            className="text-xl font-semibold mb-4"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            Корисні поради
          </Text>
          <View 
            className="p-4 rounded-xl"
            style={{ 
              backgroundColor: isDark ? colors.dark.card : colors.light.card,
              borderColor: isDark ? colors.dark.border : colors.light.border,
              borderWidth: 1
            }}
          >
            <View className="flex-row items-start">
              <Ionicons 
                name="bulb-outline" 
                size={24} 
                color={isDark ? colors.dark.accent : colors.light.accent}
                style={{ marginRight: 12, marginTop: 2 }}
              />
              <View className="flex-1">
                <Text 
                  className="text-sm font-medium mb-1"
                  style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                >
                  Завжди в'яжіть зразок!
                </Text>
                <Text 
                  className="text-xs"
                  style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                >
                  Перед початком проєкту зв'яжіть зразок 10x10 см для точного розрахунку щільності в'язання.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}