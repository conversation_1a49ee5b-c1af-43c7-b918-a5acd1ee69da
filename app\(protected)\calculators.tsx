import React, { useState } from 'react'
import { View, ScrollView, Text, Pressable, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useColorScheme } from 'nativewind'
import { colors } from '../../constants/colors'
import { Ionicons } from '@expo/vector-icons'

export default function CalculatorsScreen() {
  const { colorScheme } = useColorScheme()
  const isDark = colorScheme === 'dark'
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const showComingSoon = (calculatorName: string) => {
    Alert.alert(
      'Незабаром',
      `Калькулятор "${calculatorName}" буде доступний у наступних оновленнях додатку.`,
      [{ text: 'Зрозуміло', style: 'default' }]
    )
  }

  const calculatorCategories = [
    {
      id: 'adaptation',
      title: 'Адаптація МК',
      icon: 'document-text-outline' as const,
      calculators: [
        { id: 'mk-adaptation', title: 'Адаптація МК' }
      ]
    },
    {
      id: 'yarn',
      title: 'Калькулятор пряжі',
      icon: 'library-outline' as const,
      calculators: [
        { id: 'yarn-consumption', title: 'Витрата пряжі' },
        { id: 'yarn-folding', title: 'Розрахунок складань' },
        { id: 'additional-thread', title: 'Розрахунок додаткової нитки' },
        { id: 'gauge-calculation', title: 'Розрахунок щільності на основі зразка' }
      ]
    },
    {
      id: 'raglan-classic',
      title: 'Калькулятор моделі реглан - класичний',
      icon: 'shirt-outline' as const,
      calculators: [
        { id: 'neckline-calc', title: 'Розрахунок горловини' },
        { id: 'raglan-distribution', title: 'Розподіл петель на реглан' },
        { id: 'raglan-length', title: 'Довжина регланної лінії' },
        { id: 'raglan-increases', title: 'Прибавки реглану' },
        { id: 'yoke', title: 'Росток' },
        { id: 'yoke-adjustment', title: 'Коригування розподілу петель відповідно ростка' },
        { id: 'turning-points', title: 'Точки розвороту при в\'язанні ростка' },
        { id: 'raglan-decreases', title: 'Убавки для формування реглану при в\'язанні знизу' }
      ]
    },
    {
      id: 'underarm',
      title: 'Калькулятор петель підрізів',
      icon: 'ellipse-outline' as const,
      calculators: [
        { id: 'underarm-stitches', title: 'Калькулятор петель підрізів' }
      ]
    },
    {
      id: 'round-yoke',
      title: 'Калькулятор моделі кругла кокетка',
      icon: 'radio-button-on-outline' as const,
      calculators: [
        { id: 'round-yoke-height', title: 'Висота круглої кокетки' },
        { id: 'round-yoke-increases', title: 'Розрахунок прибавок' }
      ]
    },
    {
      id: 'sleeve',
      title: 'Калькулятор убавок і прибавок рукава',
      icon: 'remove-circle-outline' as const,
      calculators: [
        { id: 'sleeve-shaping', title: 'Калькулятор убавок і прибавок рукава' }
      ]
    },
    {
      id: 'raglan-shoulder',
      title: 'Калькулятор моделі реглан-погон',
      icon: 'medal-outline' as const,
      calculators: [
        { id: 'raglan-shoulder-calc', title: 'Реглан - погон' }
      ]
    },
    {
      id: 'dropped-shoulder',
      title: 'Калькулятор моделі спущене плече',
      icon: 'arrow-down-circle-outline' as const,
      calculators: [
        { id: 'stitches-to-cast', title: 'Скільки набрати петель' },
        { id: 'neckline-width', title: 'Ширина горловини' },
        { id: 'shoulder-width', title: 'Ширина плеча та скоси' },
        { id: 'neckline-depth', title: 'Поглиблення горловини' }
      ]
    },
    {
      id: 'v-neckline',
      title: 'Калькулятор V-горловина',
      icon: 'chevron-down-outline' as const,
      calculators: [
        { id: 'v-neck-decreases', title: 'Убавки V-горловини' },
        { id: 'v-neck-increases', title: 'Прибавки V-горловини' }
      ]
    },
    {
      id: 'accessories',
      title: 'Калькулятор петель для аксесуарів',
      icon: 'gift-outline' as const,
      calculators: [
        { id: 'hat-calc', title: 'Шапка' },
        { id: 'scarf-calc', title: 'Шарф' },
        { id: 'socks-calc', title: 'Шкарпетки' },
        { id: 'mittens-calc', title: 'Рукавички' },
        { id: 'blanket-calc', title: 'Плед' }
      ]
    }
  ]

  return (
    <SafeAreaView 
      style={{ 
        flex: 1, 
        backgroundColor: isDark ? colors.dark.background : colors.light.background 
      }}
    >
      {/* Header */}
      <View 
        className="flex-row items-center justify-between px-4 py-3"
        style={{ 
          borderBottomWidth: 1,
          borderBottomColor: isDark ? colors.dark.border : colors.light.border
        }}
      >
        <Pressable>
          <Ionicons 
            name="menu" 
            size={24} 
            color={isDark ? colors.dark.foreground : colors.light.foreground}
          />
        </Pressable>
        
        <View className="flex-row items-center gap-4">
          <Pressable>
            <Ionicons 
              name="star" 
              size={24} 
              color={isDark ? colors.dark.foreground : colors.light.foreground}
            />
          </Pressable>
          <Pressable>
            <Ionicons 
              name="notifications" 
              size={24} 
              color={isDark ? colors.dark.foreground : colors.light.foreground}
            />
          </Pressable>
        </View>
      </View>

      <ScrollView className="flex-1">
        {/* Main Title */}
        <View className="px-4 py-6">
          <Text 
            className="text-2xl font-bold text-center"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            Розрахуй і в'яжи,
          </Text>
          <Text 
            className="text-2xl font-bold text-center"
            style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
          >
            петля в петлю!
          </Text>
        </View>

        {/* Calculator Categories */}
        <View className="px-4 pb-6">
          {calculatorCategories.map((category) => (
            <View key={category.id} className="mb-4">
              {/* Category Header */}
              <Pressable
                onPress={() => toggleCategory(category.id)}
                className="flex-row items-center justify-between p-4 rounded-lg"
                style={{ 
                  backgroundColor: isDark ? colors.dark.card : colors.light.card,
                  borderColor: isDark ? colors.dark.border : colors.light.border,
                  borderWidth: 1
                }}
              >
                <View className="flex-row items-center flex-1">
                  <Ionicons 
                    name={category.icon} 
                    size={20} 
                    color={isDark ? colors.dark.primary : colors.light.primary}
                    style={{ marginRight: 12 }}
                  />
                  <Text 
                    className="text-base font-medium flex-1"
                    style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                  >
                    {category.title}
                  </Text>
                </View>
                
                <View className="flex-row items-center gap-3">
                  <Pressable>
                    <Ionicons 
                      name="star-outline" 
                      size={20} 
                      color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                    />
                  </Pressable>
                  <Ionicons 
                    name={expandedCategories.includes(category.id) ? "chevron-up" : "chevron-down"} 
                    size={20} 
                    color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                  />
                </View>
              </Pressable>

              {/* Subcategories */}
              {expandedCategories.includes(category.id) && (
                <View className="mt-2 ml-4">
                  {category.calculators.map((calculator) => (
                    <Pressable
                      key={calculator.id}
                      onPress={() => showComingSoon(calculator.title)}
                      className="flex-row items-center justify-between p-3 mb-2 rounded-lg"
                      style={{ 
                        backgroundColor: isDark ? colors.dark.muted : colors.light.muted,
                        borderColor: isDark ? colors.dark.border : colors.light.border,
                        borderWidth: 1
                      }}
                    >
                      <Text 
                        className="text-sm flex-1"
                        style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                      >
                        {calculator.title}
                      </Text>
                      
                      <View className="flex-row items-center gap-3">
                        <Pressable>
                          <Ionicons 
                            name="star-outline" 
                            size={16} 
                            color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                          />
                        </Pressable>
                        <Ionicons 
                          name="chevron-forward" 
                          size={16} 
                          color={isDark ? colors.dark.mutedForeground : colors.light.mutedForeground}
                        />
                      </View>
                    </Pressable>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>

        {/* Help Section */}
        <View className="px-4 pb-8">
          <View 
            className="p-4 rounded-xl"
            style={{ 
              backgroundColor: isDark ? colors.dark.card : colors.light.card,
              borderColor: isDark ? colors.dark.border : colors.light.border,
              borderWidth: 1
            }}
          >
            <View className="flex-row items-start">
              <Ionicons 
                name="information-circle-outline" 
                size={24} 
                color={isDark ? colors.dark.accent : colors.light.accent}
                style={{ marginRight: 12, marginTop: 2 }}
              />
              <View className="flex-1">
                <Text 
                  className="text-sm font-medium mb-1"
                  style={{ color: isDark ? colors.dark.foreground : colors.light.foreground }}
                >
                  Офлайн-доступ
                </Text>
                <Text 
                  className="text-xs"
                  style={{ color: isDark ? colors.dark.mutedForeground : colors.light.mutedForeground }}
                >
                  Всі 29 калькуляторів працюють без інтернету. Ваші розрахунки зберігаються локально та синхронізуються автоматично.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}