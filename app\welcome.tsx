import React, { useState, useEffect } from "react";
import { View, ScrollView, Dimensions, Pressable, Alert } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

import { Image } from "@/components/image";
import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { H1, H2, Muted } from "@/components/ui/typography";
import { useColorScheme } from "@/lib/useColorScheme";
import { useBiometricAuth } from "@/lib/hooks/useBiometricAuth";
import { useAuth } from "@/context/supabase-provider";

const { width } = Dimensions.get("window");

const screenshots = [
  {
    id: 1,
    title: "Калькулятори",
    description: "30+ спеціалізованих калькуляторів",
    image: require("@/assets/icon.png"), // Тимчасово використовуємо іконку
  },
  {
    id: 2,
    title: "Проєкти",
    description: "Зручний щоденник проєктів",
    image: require("@/assets/icon.png"),
  },
  {
    id: 3,
    title: "Спільнота",
    description: "Спілкування з майстрами",
    image: require("@/assets/icon.png"),
  },
  {
    id: 4,
    title: "Облік пряжі",
    description: "Керування запасами",
    image: require("@/assets/icon.png"),
  },
  {
    id: 5,
    title: "Галерея",
    description: "Натхнення та ідеї",
    image: require("@/assets/icon.png"),
  },
];

export default function WelcomeScreen() {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const { signIn } = useAuth();
  const {
    isAvailable,
    isEnrolled,
    biometricType,
    authenticate,
    getCredentials,
    getBiometricPreference
  } = useBiometricAuth();
  
  const [showScreenshots, setShowScreenshots] = useState(false);
  const [currentScreenshot, setCurrentScreenshot] = useState(0);
  const [showBiometricButton, setShowBiometricButton] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Перевіряємо чи доступна біометрична автентифікація при завантаженні
  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    if (isAvailable && isEnrolled) {
      const isEnabled = await getBiometricPreference();
      if (isEnabled) {
        const hasCredentials = await getCredentials();
        setShowBiometricButton(hasCredentials !== null);
      }
    }
  };

  const handleBiometricLogin = async () => {
    setIsAuthenticating(true);
    try {
      const success = await authenticate();
      
      if (success) {
        const credentials = await getCredentials();
        if (credentials) {
          await signIn(credentials.email, credentials.password);
          // Навігація відбудеться автоматично через AuthProvider
        }
      } else {
        Alert.alert(
          "Помилка автентифікації",
          "Не вдалося автентифікуватися за допомогою біометрії"
        );
      }
    } catch (error) {
      Alert.alert(
        "Помилка",
        "Виникла помилка при спробі входу"
      );
    } finally {
      setIsAuthenticating(false);
    }
  };

  const handleScroll = (event: any) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = event.nativeEvent.contentOffset.x / slideSize;
    const roundIndex = Math.round(index);
    setCurrentScreenshot(roundIndex);
  };

  if (showScreenshots) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1">
          {/* Header */}
          <View className="flex-row items-center justify-between p-4">
            <Pressable onPress={() => setShowScreenshots(false)}>
              <Ionicons 
                name="close" 
                size={24} 
                color={colorScheme === "dark" ? "#fff" : "#000"} 
              />
            </Pressable>
            <Text className="text-lg font-semibold">Перегляд застосунку</Text>
            <View style={{ width: 24 }} />
          </View>

          {/* Screenshot Carousel */}
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
          >
            {screenshots.map((screenshot) => (
              <View key={screenshot.id} style={{ width }} className="items-center justify-center p-8">
                <View className="bg-card rounded-2xl p-6 items-center shadow-lg">
                  <Image source={screenshot.image} className="w-48 h-48 rounded-xl mb-4" />
                  <H2 className="text-center mb-2">{screenshot.title}</H2>
                  <Muted className="text-center">{screenshot.description}</Muted>
                </View>
              </View>
            ))}
          </ScrollView>

          {/* Pagination dots */}
          <View className="flex-row justify-center items-center py-4">
            {screenshots.map((_, index) => (
              <View
                key={index}
                className={`h-2 mx-1 rounded-full ${
                  index === currentScreenshot ? "w-8 bg-primary" : "w-2 bg-muted-foreground/30"
                }`}
              />
            ))}
          </View>

          {/* Navigation */}
          <View className="flex-row justify-between items-center px-6 pb-4">
            <Pressable
              onPress={() => {
                if (currentScreenshot > 0) {
                  setCurrentScreenshot(currentScreenshot - 1);
                }
              }}
              disabled={currentScreenshot === 0}
            >
              <Text className={currentScreenshot === 0 ? "text-muted-foreground" : "text-primary"}>
                ◄ НАЗАД
              </Text>
            </Pressable>
            <Text className="text-muted-foreground">
              {currentScreenshot + 1}/{screenshots.length}
            </Text>
            <Pressable
              onPress={() => {
                if (currentScreenshot < screenshots.length - 1) {
                  setCurrentScreenshot(currentScreenshot + 1);
                }
              }}
              disabled={currentScreenshot === screenshots.length - 1}
            >
              <Text className={currentScreenshot === screenshots.length - 1 ? "text-muted-foreground" : "text-primary"}>
                ДАЛІ ►
              </Text>
            </Pressable>
          </View>

          {/* CTA Button */}
          <View className="px-6 pb-6">
            <Button
              size="lg"
              variant="default"
              onPress={() => {
                router.push("/subscription-plans");
              }}
              className="w-full"
            >
              <Text>ПРИДБАТИ ЗАРАЗ</Text>
            </Button>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background p-6">
      <View className="flex-1 items-center justify-center gap-y-6">
        {/* Logo */}
        <Text className="text-6xl">🧶</Text>
        
        {/* Title */}
        <View className="items-center gap-y-2">
          <H1 className="text-center text-3xl">Розрахуй і В'яжи,</H1>
          <H1 className="text-center text-2xl">петля в петлю!</H1>
        </View>

        {/* Features */}
        <View className="gap-y-2 px-4">
          <Text className="text-lg">Ваш помічник у в'язанні:</Text>
          <Text className="text-base">• 30+ калькуляторів</Text>
          <Text className="text-base">• Зручний щоденник проєктів</Text>
          <Text className="text-base">• Спільнота в'язальниць</Text>
          <Text className="text-base">• Облік пряжі</Text>
        </View>
      </View>

      {/* Buttons */}
      <View className="gap-y-4">
        <Button
          size="lg"
          variant="outline"
          onPress={() => setShowScreenshots(true)}
          className="w-full"
        >
          <Text>ПЕРЕГЛЯНУТИ СКРІНШОТИ</Text>
        </Button>
        
        <Button
          size="lg"
          variant="default"
          onPress={() => router.push("/subscription-plans")}
          className="w-full"
        >
          <Text>ПРИДБАТИ ПІДПИСКУ</Text>
        </Button>

        {showBiometricButton && (
          <Button
            size="lg"
            variant="secondary"
            onPress={handleBiometricLogin}
            disabled={isAuthenticating}
            className="w-full bg-green-600"
          >
            <View className="flex-row items-center justify-center gap-2">
              <Ionicons
                name="finger-print"
                size={24}
                color="white"
              />
              <Text className="text-white font-semibold">
                {isAuthenticating ? "Автентифікація..." : "Швидкий вхід"}
              </Text>
            </View>
          </Button>
        )}

        <Pressable
          onPress={() => router.push("/sign-in")}
          className="py-2"
        >
          <Text className="text-center text-primary">
            Вже маєте підписку? Увійти
          </Text>
        </Pressable>
      </View>
    </SafeAreaView>
  );
}
