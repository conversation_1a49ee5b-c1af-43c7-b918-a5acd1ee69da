# План очищення документації - "Розрахуй і В'яжи"

## 🎯 Мета
Видалити дублюючі документи та навести порядок у структурі проєкту для кращої організації та зменшення плутанини.

## 📊 Аналіз поточного стану

### Дублюючі документи (для видалення):

#### Google OAuth документи:
- ❌ `GOOGLE_OAUTH_CRITICAL_FIX_SUMMARY.md`
- ❌ `GOOGLE_OAUTH_EXPO_GO_FIX_SUMMARY.md` 
- ❌ `GOOGLE_OAUTH_EXPO_GO_FIX.md`
- ❌ `GOOGLE_OAUTH_EXPO_GO_REDIRECT_URIS.md`
- ❌ `GOOGLE_OAUTH_MOBILE_SETUP.md`
- ✅ `GOOGLE_OAUTH_SETUP.md` (залишити основний)

#### Expo налаштування:
- ❌ `EXPO_BUILD_PROPERTIES_FIX.md`
- ❌ `EXPO_GO_COMPLETE_SETUP.md`
- ❌ `EXPO_GO_TROUBLESHOOTING.md`
- ❌ `EXPO_PROJECT_SETUP.md`
- ❌ `EXPO_RUNTIME_FIX.md`
- ✅ `EXPO_SETUP_GUIDE.md` (залишити основний)

#### Автентифікація:
- ❌ `AUTH_NAVIGATION_FIX_PLAN.md`
- ❌ `AUTHENTICATION_CLEANUP_COMPLETED.md`
- ❌ `AUTHENTICATION_CLEANUP_REPORT.md`
- ❌ `NAVIGATION_FIX_SUCCESS_REPORT.md`
- ❌ `NAVIGATION_INFINITE_LOOP_FIX.md`
- ✅ `AUTH_SETUP_INSTRUCTIONS.md` (залишити основний)

#### Загальні налаштування:
- ❌ `DEVELOPMENT_BUILD_GUIDE.md`
- ❌ `SETUP_COMPLETE_GUIDE.md`
- ❌ `SETUP_COMPLETION_SUMMARY.md`
- ❌ `CRITICAL_FIXES_SUMMARY.md`
- ❌ `FIXES_SUMMARY.md`
- ❌ `CLEANUP_CHECKLIST.md`
- ❌ `NEXT_STEPS.md`
- ✅ `QUICK_START.md` (залишити основний)

#### Різне:
- ❌ `MANUAL_PAYMENT_SETUP.md`
- ❌ `MCP_SERVERS_STATUS_REPORT_20_06_2025.md`
- ❌ `MIGRATION_EXECUTION_GUIDE.md`
- ❌ `PACKAGE_MANAGER.md`
- ❌ `IOS_EXPO_GO_GUIDE.md`
- ✅ `ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md` (залишити - унікальний)
- ✅ `DEVELOPMENT_ROADMAP_POST_ANDROID.md` (залишити - актуальний)

## 📁 Структура після очищення

### Основні документи (залишити):
```
├── readme.md                                    # Головний README
├── QUICK_START.md                              # Швидкий старт
├── AUTH_SETUP_INSTRUCTIONS.md                  # Налаштування автентифікації
├── GOOGLE_OAUTH_SETUP.md                       # Google OAuth
├── EXPO_SETUP_GUIDE.md                         # Expo налаштування
├── ANDROID_EMULATOR_TROUBLESHOOTING_SOLUTION.md # Android troubleshooting
├── DEVELOPMENT_ROADMAP_POST_ANDROID.md         # Roadmap розробки
└── docs/                                       # Детальна документація
    ├── application-overview.md
    ├── components-and-styling.md
    ├── project-configuration.md
    ├── project-structure.md
    ├── state-management.md
    └── ascii-prototypes/                       # ASCII прототипи
        ├── README.md
        ├── _template.md
        ├── calculators/
        ├── projects/
        ├── settings/
        └── yarn-management/
```

## 🗑️ Файли для видалення (24 файли)

### Команди для видалення:
```bash
# Google OAuth дублікати
rm GOOGLE_OAUTH_CRITICAL_FIX_SUMMARY.md
rm GOOGLE_OAUTH_EXPO_GO_FIX_SUMMARY.md
rm GOOGLE_OAUTH_EXPO_GO_FIX.md
rm GOOGLE_OAUTH_EXPO_GO_REDIRECT_URIS.md
rm GOOGLE_OAUTH_MOBILE_SETUP.md

# Expo дублікати
rm EXPO_BUILD_PROPERTIES_FIX.md
rm EXPO_GO_COMPLETE_SETUP.md
rm EXPO_GO_TROUBLESHOOTING.md
rm EXPO_PROJECT_SETUP.md
rm EXPO_RUNTIME_FIX.md

# Автентифікація дублікати
rm AUTH_NAVIGATION_FIX_PLAN.md
rm AUTHENTICATION_CLEANUP_COMPLETED.md
rm AUTHENTICATION_CLEANUP_REPORT.md
rm NAVIGATION_FIX_SUCCESS_REPORT.md
rm NAVIGATION_INFINITE_LOOP_FIX.md

# Загальні дублікати
rm DEVELOPMENT_BUILD_GUIDE.md
rm SETUP_COMPLETE_GUIDE.md
rm SETUP_COMPLETION_SUMMARY.md
rm CRITICAL_FIXES_SUMMARY.md
rm FIXES_SUMMARY.md
rm CLEANUP_CHECKLIST.md
rm NEXT_STEPS.md

# Різне
rm MANUAL_PAYMENT_SETUP.md
rm MCP_SERVERS_STATUS_REPORT_20_06_2025.md
rm MIGRATION_EXECUTION_GUIDE.md
rm PACKAGE_MANAGER.md
rm IOS_EXPO_GO_GUIDE.md
```

## 📝 Оновлення основних документів

### 1. Оновити `readme.md`:
- Додати секцію "Швидкий старт"
- Посилання на основні документи
- Видалити застарілі посилання

### 2. Оновити `QUICK_START.md`:
- Консолідувати інформацію з видалених файлів
- Додати troubleshooting секцію
- Оновити команди та інструкції

### 3. Створити `TROUBLESHOOTING.md`:
- Консолідувати всі troubleshooting поради
- Секції: Android, iOS, Expo, OAuth, Payment

## ✅ Результат очищення

### Переваги:
- **Зменшення плутанини** - тільки актуальні документи
- **Легше навігація** - чітка структура
- **Менше дублювання** - одне джерело істини
- **Кращий onboarding** - зрозумілі інструкції

### Метрики:
- **Було**: ~35 документів у root
- **Стане**: ~8 основних документів
- **Видалено**: 24 дублюючих файли
- **Економія**: ~70% зменшення кількості файлів

## 🚀 Наступні кроки

1. ✅ Створити план очищення (цей документ)
2. 🔄 Виконати видалення файлів
3. 🔄 Оновити основні документи
4. 🔄 Перевірити посилання
5. 🔄 Оновити банк пам'яті
6. 🔄 Створити екран калькуляторів

---

*Створено: 24.06.2025*  
*Статус: План готовий до виконання*