const https = require('https');

// Конфігурація Supabase
const SUPABASE_URL = 'https://xaeztaeqyjubmpgjxcgh.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhhZXp0YWVxeWp1Ym1wZ2p4Y2doIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2NzI1OCwiZXhwIjoyMDY1NjQzMjU4fQ.D9ecOYjmUFGaEsC36gUduuOQlXAMIAx9Cuchi01or8M';

// Дані користувача для створення профілю
const USER_ID = 'f15eb141-46b2-4b87-b5f0-5542f60b27a5';
const USER_EMAIL = '<EMAIL>';
const USER_NAME = 'Public Relations';

console.log('🔧 REST API Fix Script - Створення відсутнього профілю');
console.log('================================================');

async function createProfile() {
  return new Promise((resolve, reject) => {
    const profileData = {
      id: USER_ID,
      email: USER_EMAIL,
      name: USER_NAME,
      subscription_type: 'free'
    };

    const postData = JSON.stringify(profileData);
    
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: '/rest/v1/profiles',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY,
        'Prefer': 'return=representation'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Статус відповіді: ${res.statusCode}`);
        console.log(`📋 Заголовки:`, res.headers);
        
        if (res.statusCode === 201 || res.statusCode === 200) {
          console.log('✅ Профіль успішно створено!');
          console.log('📄 Дані профілю:', JSON.parse(data));
          resolve(JSON.parse(data));
        } else {
          console.log('❌ Помилка створення профілю');
          console.log('📄 Відповідь сервера:', data);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Помилка запиту:', error);
      reject(error);
    });

    console.log('📤 Відправляємо POST запит до /rest/v1/profiles');
    console.log('📋 Дані профілю:', profileData);
    
    req.write(postData);
    req.end();
  });
}

async function verifyProfile() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'xaeztaeqyjubmpgjxcgh.supabase.co',
      port: 443,
      path: `/rest/v1/profiles?id=eq.${USER_ID}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          const profiles = JSON.parse(data);
          if (profiles.length > 0) {
            console.log('✅ Профіль знайдено в базі даних!');
            console.log('📄 Дані профілю:', profiles[0]);
            resolve(profiles[0]);
          } else {
            console.log('❌ Профіль не знайдено');
            reject(new Error('Profile not found'));
          }
        } else {
          console.log('❌ Помилка перевірки профілю');
          console.log('📄 Відповідь сервера:', data);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Помилка запиту:', error);
      reject(error);
    });

    console.log('🔍 Перевіряємо існування профілю...');
    req.end();
  });
}

async function main() {
  try {
    console.log(`👤 Користувач: ${USER_EMAIL} (ID: ${USER_ID})`);
    console.log('');

    // Спочатку перевіримо, чи існує профіль
    try {
      await verifyProfile();
      console.log('ℹ️  Профіль вже існує, створення не потрібне');
      return;
    } catch (error) {
      console.log('ℹ️  Профіль не існує, створюємо новий...');
    }

    // Створюємо профіль
    await createProfile();
    
    // Перевіряємо створення
    console.log('');
    console.log('🔍 Фінальна перевірка...');
    await verifyProfile();
    
    console.log('');
    console.log('🎉 УСПІХ! Профіль створено та перевірено');
    console.log('');
    console.log('📋 Наступні кроки:');
    console.log('1. Тестуйте автентифікацію з:');
    console.log(`   Email: ${USER_EMAIL}`);
    console.log('   Password: TempPass123!');
    console.log('2. Запустіть діагностичний скрипт для підтвердження');
    
  } catch (error) {
    console.error('');
    console.error('💥 КРИТИЧНА ПОМИЛКА:', error.message);
    console.error('');
    console.error('🔧 Можливі рішення:');
    console.error('1. Перевірте Service Role Key в .env.local');
    console.error('2. Перевірте RLS політики для таблиці profiles');
    console.error('3. Перевірте структуру таблиці profiles');
    process.exit(1);
  }
}

main();