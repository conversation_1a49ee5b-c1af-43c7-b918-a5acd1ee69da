import "../global.css";

import { Stack } from "expo-router";
import { StatusBar } from 'expo-status-bar'
import { GestureHandlerRootView } from 'react-native-gesture-handler'

import { AuthProvider } from "@/context/supabase-provider";
import { useColorScheme } from "@/lib/useColorScheme";
import { colors } from "@/constants/colors";

export default function AppLayout() {
	const { colorScheme } = useColorScheme();

	return (
		<GestureHandlerRootView style={{ flex: 1 }}>
			<AuthProvider>
				<StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
				<Stack screenOptions={{ headerShown: false, gestureEnabled: false }}>
						{/* Захищені маршрути - доступні тільки з активною підпискою */}
						<Stack.Screen name="(protected)" />
						
						{/* Маршрути підписки - доступні тільки автентифікованим користувачам */}
						<Stack.Screen
							name="subscription-plans"
							options={{
								headerShown: true,
								headerTitle: "Оберіть підписку",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
							}}
						/>
						
						<Stack.Screen
							name="payment"
							options={{
								headerShown: true,
								headerTitle: "Оплата",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
							}}
						/>
						
						{/* Публічні маршрути автентифікації */}
						<Stack.Screen name="welcome" />
						<Stack.Screen
							name="sign-up"
							options={{
								presentation: "modal",
								headerShown: true,
								headerTitle: "Реєстрація",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
								gestureEnabled: true,
							}}
						/>
						<Stack.Screen
							name="sign-in"
							options={{
								presentation: "modal",
								headerShown: true,
								headerTitle: "Вхід",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
								gestureEnabled: true,
							}}
						/>
						
						{/* Правові документи */}
						<Stack.Screen
							name="terms"
							options={{
								headerShown: true,
								headerTitle: "Умови використання",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
							}}
						/>
						<Stack.Screen
							name="privacy"
							options={{
								headerShown: true,
								headerTitle: "Політика конфіденційності",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
							}}
						/>
						
						{/* Відновлення паролю */}
						<Stack.Screen
							name="forgot-password"
							options={{
								presentation: "modal",
								headerShown: true,
								headerTitle: "Відновлення паролю",
								headerStyle: {
									backgroundColor:
										colorScheme === "dark"
											? colors.dark.background
											: colors.light.background,
								},
								headerTintColor:
									colorScheme === "dark"
										? colors.dark.foreground
										: colors.light.foreground,
								gestureEnabled: true,
							}}
						/>
						
						{/* Результати платежів */}
						<Stack.Screen name="payment-success" />
						<Stack.Screen name="payment-error" />
				</Stack>
			</AuthProvider>
		</GestureHandlerRootView>
	);
}
