import React, { useEffect } from "react";
import { View, ScrollView } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { H1, H2 } from "@/components/ui/typography";
export default function PaymentSuccess() {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Отримуємо параметри платежу
  const planName = params.planName as string || "Підписка";
  const planPrice = params.planPrice as string || "";
  const transactionId = params.transactionId as string || "";

  const handleContinue = () => {
    // Переходимо до головного екрану додатку
    router.replace("/(protected)");
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 justify-center items-center p-6">
          {/* Success Icon */}
          <View className="w-24 h-24 bg-green-100 dark:bg-green-900/30 rounded-full items-center justify-center mb-6">
            <Ionicons name="checkmark-circle" size={48} color="#22c55e" />
          </View>

          {/* Success Message */}
          <H1 className="text-center mb-4 text-green-600 dark:text-green-400">
            Оплата успішна!
          </H1>

          <Text className="text-center text-muted-foreground mb-8 text-base leading-6">
            Дякуємо за покупку! Ваша підписка "{planName}" активована.
            {"\n"}Тепер ви маєте доступ до всіх функцій додатку.
          </Text>

          {/* Payment Details */}
          <View className="w-full bg-card border border-border rounded-xl p-4 mb-8">
            <H2 className="mb-4">Деталі платежу</H2>
            
            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">План:</Text>
                <Text className="font-medium">{planName}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Сума:</Text>
                <Text className="font-medium">{planPrice}</Text>
              </View>
              
              {transactionId && (
                <View className="flex-row justify-between items-center">
                  <Text className="text-muted-foreground">ID транзакції:</Text>
                  <Text className="font-mono text-sm">{transactionId}</Text>
                </View>
              )}
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Дата:</Text>
                <Text className="font-medium">
                  {new Date().toLocaleDateString('uk-UA')}
                </Text>
              </View>
            </View>
          </View>

          {/* Features List */}
          <View className="w-full mb-8">
            <H2 className="mb-4 text-center">Що тепер доступно:</H2>
            
            <View className="space-y-3">
              {[
                "29 спеціалізованих калькуляторів пряжі",
                "Управління проєктами в'язання",
                "Каталог пряжі з фото",
                "Лічильники рядів",
                "Галерея натхнення",
                "Спільнота майстрів",
                "Синхронізація між пристроями",
                "Офлайн-режим роботи"
              ].map((feature, index) => (
                <View key={index} className="flex-row items-center">
                  <Ionicons 
                    name="checkmark-circle" 
                    size={20} 
                    color="#22c55e" 
                    style={{ marginRight: 12 }}
                  />
                  <Text className="flex-1">{feature}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Continue Button */}
      <View className="p-6 border-t border-border">
        <Button
          size="lg"
          variant="default"
          onPress={handleContinue}
          className="w-full"
        >
          <Text>Почати користуватися</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}