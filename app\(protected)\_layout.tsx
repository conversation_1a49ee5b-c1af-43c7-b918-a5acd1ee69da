import React from 'react'
import { Stack } from 'expo-router'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'

/**
 * Layout для захищених маршрутів
 * Всі екрани в цій групі потребують активної підписки
 */
export default function ProtectedLayout() {
  return (
    <ProtectedRoute requireSubscription={false} fallbackRoute="/welcome">
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" />
        <Stack.Screen name="projects" />
        <Stack.Screen name="calculators" />
        <Stack.Screen name="yarn" />
        <Stack.Screen name="home" />
        <Stack.Screen name="modal" />
        <Stack.Screen name="(tabs)" />
      </Stack>
    </ProtectedRoute>
  )
}
