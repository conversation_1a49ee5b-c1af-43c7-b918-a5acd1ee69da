# ninja log v5
2	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
16	2372	7724926293709201	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	b22c86c2e3785106
60	2942	7724926299479889	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	27b1f87b0685fcfd
50	2493	7724926294726113	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	c6fe8c798d518a0e
111	2675	7724926296612936	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22669480df1bba5d525080f8cd885cbf/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	5ab9c4da9f75758a
2372	5537	7724926325486442	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	5a2d382eb32ac28
83	2536	7724926295333981	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	7c8a931e04b8903b
63	2955	7724926299567084	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	2843e30ab4a0b33a
76	3856	7724926308647048	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	d3a96b221ca3cb1d
3298	6683	7724926336863159	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	2fd4b833283b20ef
107	3231	7724926302287822	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	c2893fa8d0c7faae
14	2780	7724926297627467	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	c4bfdae0be776823
47	4874	7724926318744219	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	6c065cb8729a7d22
95	4726	7724926317305413	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9fb98d8d4834f358bdc2633bd2f84fbe/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	742118b101020351
12	3133	7724926301235024	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	6cbd1fc1e01e04e5
2956	5899	7724926329091886	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	17d399fc6ada30ef
7	7901	7724926348702216	CMakeFiles/appmodules.dir/C_/expo-supabase-starter/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e7f7d4ad5d332a1a
69	3392	7724926303708250	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	d3d657a6cf68f5b8
24	3353	7724926303332022	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ac4b2202e291cc8f
86	3361	7724926303193930	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	3644d83f5fa68f8b
3133	7258	7724926342543510	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	ef7a026aa4939cf2
72	3297	7724926302961845	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	d89fd6411c30d9f3
19	3585	7724926305789577	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	30b2f766a512fd5d
27	3602	7724926305957830	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	775590fadf9a495b
66	3514	7724926305143539	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	26b118a13cbc9b16
2509	5211	7724926322233542	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9192dfeb14bab75dff9a3d59da63c491/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	df34924bc254abff
21	3615	7724926306195293	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	422ef54f4fab6af1
54	3605	7724926306140305	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	d444fef21aa4c4ed
10	3710	7724926307180951	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	418f988a647ecc37
79	3789	7724926307999762	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	5377daf40fd55079
91	3570	7724926305580289	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	544a2f6c193c494d
2494	5439	7724926324456454	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afd6522afceba2d82bdea0dbcd01a58a/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	1753544f21787da3
29	4429	7724926314213885	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	c141fbf3e44deac8
99	3964	7724926309700898	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4da99a9f477dced1fad22751c9848ed1/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	2891073e282cbadf
2675	5825	7724926328369059	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	b5a4ad0328b7d001
9	3858	7723984137503803	CMakeFiles/appmodules.dir/OnLoad.cpp.o	814cfc52f682f5da
2537	5766	7724926327761834	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22669480df1bba5d525080f8cd885cbf/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	b69e257ace8a87db
2942	5945	7724926329566041	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	796351016b06cccd
57	4518	7724926314991837	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	7739434962cede7c
37	3384	7724926303487721	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	375700a3cb4a6761
32	3058	7724926300504806	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	d9ad768d1ac6063a
44	2508	7724926295087735	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	9667629fe514eda5
102	4904	7724926319099112	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	92c88d48692a3025
3058	6078	7724926330921912	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	3e7eb35f9fecd1a2
5766	5840	7724926328483194	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libreact_codegen_safeareacontext.so	f1ad278ccec7cd32
3384	5158	7724926321723741	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	9fe9fe2b809ad6d8
3077	5490	7723984153953443	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	67ff46c97b57b13d
7258	7343	7724926343428864	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libreact_codegen_rnscreens.so	62306c980230f947
3229	5697	7723984156050078	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	263f8ee8b2f83024
3217	5672	7723984155749913	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	175d432f3acd304c
2781	5902	7724926329144671	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	2a366a9fdc70cd0
3361	6062	7724926330757724	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	f645c791fc4e30e3
3392	5615	7724926326274617	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	a514b937b7a1306e
3231	6057	7724926330699030	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	2d28c1c653f8d9a4
3353	6449	7724926334534109	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b6cf528d886ea60d
35	4074	7724926310764837	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	bc40d668f8fadf79
7901	7997	7724926349939030	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libappmodules.so	3ad053b234233c24
41	3784	7724926307910550	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	7ceb0e48285735e9
1	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
25	2343	7727172851983783	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	b22c86c2e3785106
54	2406	7727172852562885	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o	9667629fe514eda5
64	2557	7727172854047654	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	c6fe8c798d518a0e
117	2635	7727172855018793	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	7c8a931e04b8903b
15	2775	7727172856344104	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	c4bfdae0be776823
84	2876	7727172857303934	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	2843e30ab4a0b33a
48	2958	7727172857849631	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o	d9ad768d1ac6063a
58	2966	7727172858163991	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	27b1f87b0685fcfd
20	3054	7727172859075371	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ac4b2202e291cc8f
13	3097	7727172859488931	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	6cbd1fc1e01e04e5
127	3141	7727172859959295	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	c2893fa8d0c7faae
77	3162	7727172860260773	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	d89fd6411c30d9f3
37	3174	7727172859969300	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o	375700a3cb4a6761
102	3282	7727172861192566	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	3644d83f5fa68f8b
34	3297	7727172861067382	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o	775590fadf9a495b
28	3440	7727172862790262	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	30b2f766a512fd5d
42	3451	7727172862985894	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o	7ceb0e48285735e9
93	3459	7727172862945916	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o	d3d657a6cf68f5b8
23	3528	7727172863934236	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	422ef54f4fab6af1
72	3576	7727172864302833	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	26b118a13cbc9b16
18	3607	7727172864628896	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	418f988a647ecc37
112	3608	7727172864543614	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	544a2f6c193c494d
81	3610	7727172864734357	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	5377daf40fd55079
68	3643	7727172865108972	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	d444fef21aa4c4ed
88	3902	7727172867591504	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	d3a96b221ca3cb1d
45	3922	7727172867761883	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o	bc40d668f8fadf79
107	3949	7727172868139240	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4da99a9f477dced1fad22751c9848ed1/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	2891073e282cbadf
10	4139	7727172869993749	CMakeFiles/appmodules.dir/OnLoad.cpp.o	814cfc52f682f5da
61	4450	7727172872976962	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	7739434962cede7c
31	4511	7727172873687720	RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o	c141fbf3e44deac8
2344	4770	7727172876388495	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22669480df1bba5d525080f8cd885cbf/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	5ab9c4da9f75758a
51	4807	7727172876553747	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	6c065cb8729a7d22
98	4813	7727172876766080	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9fb98d8d4834f358bdc2633bd2f84fbe/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	742118b101020351
122	4955	7727172878161949	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/dc23281abf377144c2329fc2c2fcf16f/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	92c88d48692a3025
3142	5281	7727172881490244	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	9fe9fe2b809ad6d8
2636	5607	7727172884744982	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9192dfeb14bab75dff9a3d59da63c491/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	df34924bc254abff
2776	5785	7727172886465543	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/afd6522afceba2d82bdea0dbcd01a58a/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	1753544f21787da3
2406	5860	7727172887267623	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/00c0451680cce4bd48fcff87c890d394/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	5a2d382eb32ac28
3098	5875	7727172887390227	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	67ff46c97b57b13d
3162	5904	7727172887723595	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	a514b937b7a1306e
3297	5972	7727172888432504	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	263f8ee8b2f83024
3441	6053	7727172889248175	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	175d432f3acd304c
2557	6115	7727172889842936	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22669480df1bba5d525080f8cd885cbf/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	b69e257ace8a87db
6116	6220	7727172890795842	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libreact_codegen_safeareacontext.so	f1ad278ccec7cd32
3054	6231	7727172891058081	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	b5a4ad0328b7d001
2958	6253	7727172891248287	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	2a366a9fdc70cd0
2966	6261	7727172891348396	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	17d399fc6ada30ef
3174	6359	7727172892340752	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	f645c791fc4e30e3
2876	6362	7727172892340752	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e510fbe5793940f32a22732e2ba344b/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	796351016b06cccd
3528	6528	7727172893992359	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fe18ceeb03d1c5db1bfae0d1985477a0/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	2d28c1c653f8d9a4
3282	6534	7727172894081450	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	3e7eb35f9fecd1a2
3577	6784	7727172896491648	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/879b5302180341c42090357e4f59d735/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b6cf528d886ea60d
3451	7056	7727172899210711	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/edbf3156d36c595c2e497b041316d50d/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	2fd4b833283b20ef
3459	7741	7727172905993069	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e5835192c545f1b33f4ac2f674d4f1ec/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	ef7a026aa4939cf2
7742	7826	7727172906893949	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libreact_codegen_rnscreens.so	62306c980230f947
8	8412	7727172912129369	CMakeFiles/appmodules.dir/C_/expo-supabase-starter/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e7f7d4ad5d332a1a
8412	8511	7727172913691067	C:/expo-supabase-starter/android/app/build/intermediates/cxx/Debug/4626513p/obj/x86_64/libappmodules.so	3ad053b234233c24
1	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
0	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
0	13	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
0	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
0	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
1	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
0	15	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
1	16	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
1	14	0	C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64/CMakeFiles/cmake.verify_globs	7caf91463316304c
