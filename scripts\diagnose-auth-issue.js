/**
 * Скрипт для діагностики проблеми з автентифікацією користувача
 * Перевіряє всі аспекти автентифікації в Supabase
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Конфігурація Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Створюємо клієнти
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const TARGET_EMAIL = '<EMAIL>';

console.log('🔍 ДІАГНОСТИКА АВТЕНТИФІКАЦІЇ');
console.log('=' .repeat(50));
console.log(`📧 Цільовий користувач: ${TARGET_EMAIL}`);
console.log(`🌐 Supabase URL: ${supabaseUrl}`);
console.log('');

async function checkSupabaseConnection() {
    console.log('1️⃣ ПЕРЕВІРКА З\'ЄДНАННЯ З SUPABASE');
    console.log('-'.repeat(40));
    
    try {
        // Перевіряємо anon ключ
        const { data: anonTest, error: anonError } = await supabaseAnon
            .from('profiles')
            .select('count')
            .limit(1);
            
        if (anonError) {
            console.log('❌ Anon ключ не працює:', anonError.message);
            return false;
        } else {
            console.log('✅ Anon ключ працює');
        }
        
        // Перевіряємо service role ключ
        const { data: adminTest, error: adminError } = await supabaseAdmin
            .from('profiles')
            .select('count')
            .limit(1);
            
        if (adminError) {
            console.log('❌ Service Role ключ не працює:', adminError.message);
            return false;
        } else {
            console.log('✅ Service Role ключ працює');
        }
        
        return true;
    } catch (error) {
        console.log('❌ Помилка з\'єднання:', error.message);
        return false;
    }
}

async function checkUserInAuthTable() {
    console.log('\n2️⃣ ПЕРЕВІРКА КОРИСТУВАЧА В auth.users');
    console.log('-'.repeat(40));
    
    try {
        // Використовуємо admin клієнт для доступу до auth.users
        const { data, error } = await supabaseAdmin.auth.admin.listUsers();
        
        if (error) {
            console.log('❌ Помилка отримання користувачів:', error.message);
            return null;
        }
        
        const user = data.users.find(u => u.email === TARGET_EMAIL);
        
        if (user) {
            console.log('✅ Користувач знайдений в auth.users');
            console.log(`   📧 Email: ${user.email}`);
            console.log(`   🆔 ID: ${user.id}`);
            console.log(`   📅 Створено: ${user.created_at}`);
            console.log(`   ✉️ Email підтверджено: ${user.email_confirmed_at ? '✅ Так' : '❌ Ні'}`);
            console.log(`   📱 Телефон підтверджено: ${user.phone_confirmed_at ? '✅ Так' : '❌ Ні'}`);
            console.log(`   🔐 Останній вхід: ${user.last_sign_in_at || 'Ніколи'}`);
            console.log(`   🎭 Провайдери: ${user.app_metadata?.providers?.join(', ') || 'email'}`);
            
            if (!user.email_confirmed_at) {
                console.log('⚠️  УВАГА: Email не підтверджений - це може бути причиною помилки!');
            }
            
            return user;
        } else {
            console.log('❌ Користувач НЕ знайдений в auth.users');
            return null;
        }
    } catch (error) {
        console.log('❌ Помилка перевірки auth.users:', error.message);
        return null;
    }
}

async function checkUserProfile(userId) {
    console.log('\n3️⃣ ПЕРЕВІРКА ПРОФІЛЮ В profiles');
    console.log('-'.repeat(40));
    
    if (!userId) {
        console.log('⏭️ Пропускаємо - користувач не знайдений в auth.users');
        return null;
    }
    
    try {
        const { data, error } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();
            
        if (error) {
            if (error.code === 'PGRST116') {
                console.log('❌ Профіль НЕ знайдений в profiles таблиці');
                console.log('💡 Можливо, тригер handle_new_user() не спрацював');
                return null;
            } else {
                console.log('❌ Помилка отримання профілю:', error.message);
                return null;
            }
        }
        
        console.log('✅ Профіль знайдений в profiles');
        console.log(`   📧 Email: ${data.email}`);
        console.log(`   👤 Ім'я: ${data.name || 'Не вказано'}`);
        console.log(`   💳 Підписка: ${data.subscription_type}`);
        console.log(`   📅 Створено: ${data.created_at}`);
        console.log(`   🔄 Оновлено: ${data.updated_at}`);
        
        return data;
    } catch (error) {
        console.log('❌ Помилка перевірки профілю:', error.message);
        return null;
    }
}

async function checkRLSPolicies() {
    console.log('\n4️⃣ ПЕРЕВІРКА RLS ПОЛІТИК');
    console.log('-'.repeat(40));
    
    try {
        // Перевіряємо RLS політики для profiles
        const { data: rlsData, error: rlsError } = await supabaseAdmin
            .rpc('pg_policies')
            .select('*')
            .eq('tablename', 'profiles');
            
        if (rlsError) {
            console.log('⚠️ Не вдалося перевірити RLS політики напряму');
            console.log('   Перевіряємо через спробу доступу...');
        } else {
            console.log('✅ RLS політики знайдені для profiles');
        }
        
        // Тестуємо доступ до profiles через anon клієнт (без автентифікації)
        const { data: anonAccess, error: anonError } = await supabaseAnon
            .from('profiles')
            .select('*')
            .limit(1);
            
        if (anonError) {
            if (anonError.code === '42501' || anonError.message.includes('RLS')) {
                console.log('✅ RLS працює правильно - анонімний доступ заборонений');
            } else {
                console.log('❌ Неочікувана помилка RLS:', anonError.message);
            }
        } else {
            console.log('⚠️ УВАГА: RLS може не працювати - анонімний доступ дозволений');
        }
        
    } catch (error) {
        console.log('❌ Помилка перевірки RLS:', error.message);
    }
}

async function testAuthentication() {
    console.log('\n5️⃣ ТЕСТУВАННЯ АВТЕНТИФІКАЦІЇ');
    console.log('-'.repeat(40));
    
    // Тестуємо з правильним паролем (якщо знаємо)
    console.log('🔐 Тестування автентифікації з тестовим паролем...');
    
    const testPasswords = [
        'password123',
        'test123',
        'knitting123',
        '123456',
        'password'
    ];
    
    for (const password of testPasswords) {
        try {
            const { data, error } = await supabaseAnon.auth.signInWithPassword({
                email: TARGET_EMAIL,
                password: password
            });
            
            if (error) {
                if (error.message === 'Invalid login credentials') {
                    console.log(`❌ Пароль "${password}" - неправильний`);
                } else if (error.message.includes('Email not confirmed')) {
                    console.log(`⚠️ Пароль "${password}" - email не підтверджений`);
                    return { success: false, reason: 'email_not_confirmed' };
                } else {
                    console.log(`❌ Пароль "${password}" - інша помилка: ${error.message}`);
                }
            } else {
                console.log(`✅ Пароль "${password}" - ПРАВИЛЬНИЙ!`);
                console.log(`   🎯 Користувач увійшов: ${data.user.email}`);
                
                // Виходимо після успішного входу
                await supabaseAnon.auth.signOut();
                return { success: true, password: password };
            }
        } catch (error) {
            console.log(`❌ Помилка тестування паролю "${password}":`, error.message);
        }
    }
    
    return { success: false, reason: 'wrong_password' };
}

async function checkTriggerFunction() {
    console.log('\n6️⃣ ПЕРЕВІРКА ТРИГЕР ФУНКЦІЇ');
    console.log('-'.repeat(40));
    
    try {
        // Перевіряємо чи існує функція handle_new_user
        const { data, error } = await supabaseAdmin
            .rpc('pg_get_functiondef', { 
                funcoid: 'public.handle_new_user()' 
            });
            
        if (error) {
            console.log('⚠️ Не вдалося перевірити функцію handle_new_user напряму');
        } else {
            console.log('✅ Функція handle_new_user існує');
        }
        
        // Перевіряємо тригер
        const { data: triggerData, error: triggerError } = await supabaseAdmin
            .from('information_schema.triggers')
            .select('*')
            .eq('trigger_name', 'on_auth_user_created');
            
        if (triggerError) {
            console.log('⚠️ Не вдалося перевірити тригер напряму');
        } else if (triggerData && triggerData.length > 0) {
            console.log('✅ Тригер on_auth_user_created існує');
        } else {
            console.log('❌ Тригер on_auth_user_created НЕ знайдений');
        }
        
    } catch (error) {
        console.log('❌ Помилка перевірки тригера:', error.message);
    }
}

async function suggestSolutions(authResult, user, profile) {
    console.log('\n7️⃣ РЕКОМЕНДАЦІЇ ДЛЯ ВИРІШЕННЯ');
    console.log('-'.repeat(40));
    
    if (!user) {
        console.log('🔧 РІШЕННЯ: Користувач не існує');
        console.log('   1. Створіть користувача через Supabase Dashboard');
        console.log('   2. Або зареєструйте користувача через додаток');
        console.log('   3. Перевірте правильність email адреси');
        return;
    }
    
    if (!user.email_confirmed_at) {
        console.log('🔧 РІШЕННЯ: Email не підтверджений');
        console.log('   1. Підтвердіть email через Supabase Dashboard:');
        console.log('      - Authentication > Users > Знайти користувача > Confirm email');
        console.log('   2. Або надішліть новий лист підтвердження');
        console.log('   3. Перевірте spam папку користувача');
        return;
    }
    
    if (!profile) {
        console.log('🔧 РІШЕННЯ: Профіль не створений');
        console.log('   1. Створіть профіль вручну:');
        console.log(`      INSERT INTO profiles (id, email, name) VALUES ('${user.id}', '${user.email}', 'User Name');`);
        console.log('   2. Перевірте тригер handle_new_user()');
        console.log('   3. Перевірте RLS політики');
        return;
    }
    
    if (authResult.reason === 'wrong_password') {
        console.log('🔧 РІШЕННЯ: Неправильний пароль');
        console.log('   1. Скиньте пароль через Supabase Dashboard:');
        console.log('      - Authentication > Users > Знайти користувача > Send password reset');
        console.log('   2. Або використайте функцію "Забули пароль?" в додатку');
        console.log('   3. Встановіть новий пароль через Dashboard');
        return;
    }
    
    console.log('🔧 ЗАГАЛЬНІ РЕКОМЕНДАЦІЇ:');
    console.log('   1. Перевірте налаштування Supabase проєкту');
    console.log('   2. Перевірте CORS налаштування');
    console.log('   3. Перевірте логи в Supabase Dashboard');
    console.log('   4. Спробуйте автентифікацію через Dashboard');
}

async function main() {
    try {
        // Перевірка з'єднання
        const connectionOk = await checkSupabaseConnection();
        if (!connectionOk) {
            console.log('\n❌ КРИТИЧНА ПОМИЛКА: Не вдалося підключитися до Supabase');
            return;
        }
        
        // Перевірка користувача в auth.users
        const user = await checkUserInAuthTable();
        
        // Перевірка профілю
        const profile = await checkUserProfile(user?.id);
        
        // Перевірка RLS
        await checkRLSPolicies();
        
        // Тестування автентифікації
        const authResult = await testAuthentication();
        
        // Перевірка тригера
        await checkTriggerFunction();
        
        // Рекомендації
        await suggestSolutions(authResult, user, profile);
        
        console.log('\n' + '='.repeat(50));
        console.log('✅ ДІАГНОСТИКА ЗАВЕРШЕНА');
        
        // Підсумок
        console.log('\n📊 ПІДСУМОК:');
        console.log(`   Користувач в auth.users: ${user ? '✅' : '❌'}`);
        console.log(`   Email підтверджений: ${user?.email_confirmed_at ? '✅' : '❌'}`);
        console.log(`   Профіль створений: ${profile ? '✅' : '❌'}`);
        console.log(`   Автентифікація: ${authResult.success ? '✅' : '❌'}`);
        
    } catch (error) {
        console.log('\n❌ КРИТИЧНА ПОМИЛКА:', error.message);
        console.log('Стек:', error.stack);
    }
}

// Запуск діагностики
main();