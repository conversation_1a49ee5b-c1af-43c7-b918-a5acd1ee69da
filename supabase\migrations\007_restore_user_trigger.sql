-- Восстановление триггера для автоматического создания профилей пользователей
-- Дата: 28.06.2025
-- Цель: Исправить ошибку "Database error saving new user"

-- Удаляем старый триггер если существует
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Удаляем старую функцию если существует
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Создаем улучшенную функцию для создания профиля
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Логируем создание нового пользователя
  RAISE LOG 'Creating profile for new user: %', NEW.email;
  
  -- Создаем профиль с обработкой ошибок
  BEGIN
    INSERT INTO public.profiles (id, email, name, subscription_type, created_at, updated_at)
    VALUES (
      NEW.id, 
      NEW.email, 
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      'free',
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Profile created successfully for user: %', NEW.email;
    
  EXCEPTION WHEN OTHERS THEN
    -- Логируем ошибку, но не прерываем процесс регистрации
    RAISE LOG 'Error creating profile for user %: %', NEW.email, SQLERRM;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Создаем триггер
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Добавляем политику для INSERT если не существует
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'profiles' 
    AND policyname = 'Users can insert own profile'
  ) THEN
    CREATE POLICY "Users can insert own profile" ON public.profiles
      FOR INSERT WITH CHECK (auth.uid() = id);
  END IF;
END $$;

-- User profile trigger restored successfully
