import React from 'react'
import { Redirect } from 'expo-router'
import { useAuth } from '@/context/supabase-provider'
import { LoadingScreen } from '@/components/ui/LoadingScreen'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireSubscription?: boolean
  fallbackRoute?: string
}

/**
 * Компонент для захисту маршрутів
 * Перевіряє автентифікацію користувача
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireSubscription = false, // Поки що не перевіряємо підписку
  fallbackRoute,
}) => {
  const { initialized, session } = useAuth()

  // Показуємо завантаження поки ініціалізується система
  if (!initialized) {
    return <LoadingScreen message="Перевірка доступу..." />
  }

  // Якщо користувач не увійшов - перенаправляємо на welcome
  if (!session) {
    return <Redirect href={fallbackRoute || "/welcome"} />
  }

  // TODO: Додати перевірку підписки коли буде реалізована
  // if (requireSubscription && !hasActiveSubscription) {
  //   return <Redirect href="/subscription-plans" />
  // }

  // Все ОК - показуємо контент
  return <>{children}</>
}

export default ProtectedRoute