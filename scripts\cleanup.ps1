Write-Host "Stopping Metro bundler..."
taskkill /f /im node.exe 2>$null

Write-Host "Stopping Expo processes..."
taskkill /f /im expo.exe 2>$null

Write-Host "Stopping Android emulator..."
taskkill /f /im qemu-system-x86_64.exe 2>$null
taskkill /f /im emulator.exe 2>$null

Write-Host "Clearing ports..."
$processes = netstat -aon | findstr ":8081"
if ($processes) {
    $processes | ForEach-Object {
        $processId = ($_ -split '\s+')[-1]
        taskkill /PID $processId /F 2>$null
    }
}

Write-Host "Cleanup completed!"
