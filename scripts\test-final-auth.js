const { createClient } = require('@supabase/supabase-js');

// Конфігурація Supabase
const supabaseUrl = 'https://xaeztaeqyjubmpgjxcgh.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhhZXp0YWVxeWp1Ym1wZ2p4Y2doIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjcyNTgsImV4cCI6MjA2NTY0MzI1OH0.SxQsKsnRmLCO0wTNkPRxlQVzAoSrGj0kT_MyNH6EFmI';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TempPass123!';

console.log('🔐 ФІНАЛЬНИЙ ТЕСТ АВТЕНТИФІКАЦІЇ');
console.log('================================');

async function testAuthentication() {
  try {
    console.log(`👤 Тестуємо вхід для: ${TEST_EMAIL}`);
    console.log(`🔑 Пароль: ${TEST_PASSWORD}`);
    console.log('');

    // Спроба входу
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (error) {
      console.log('❌ ПОМИЛКА АВТЕНТИФІКАЦІЇ:');
      console.log(`   Код: ${error.message}`);
      console.log(`   Статус: ${error.status || 'N/A'}`);
      
      if (error.message.includes('Invalid login credentials')) {
        console.log('');
        console.log('🔧 МОЖЛИВІ ПРИЧИНИ:');
        console.log('1. Тимчасовий пароль TempPass123! не встановився правильно');
        console.log('2. Потрібно скинути пароль через Supabase Dashboard');
        console.log('3. Користувач заблокований або деактивований');
        console.log('');
        console.log('📋 РЕКОМЕНДАЦІЇ:');
        console.log('1. Увійдіть в Supabase Dashboard');
        console.log('2. Authentication > Users > <EMAIL>');
        console.log('3. Натисніть "Send password reset email"');
        console.log('4. Або встановіть новий пароль вручну');
      }
      
      return false;
    }

    if (data.user) {
      console.log('✅ АВТЕНТИФІКАЦІЯ УСПІШНА!');
      console.log('');
      console.log('👤 Дані користувача:');
      console.log(`   ID: ${data.user.id}`);
      console.log(`   Email: ${data.user.email}`);
      console.log(`   Email підтверджено: ${data.user.email_confirmed_at ? '✅' : '❌'}`);
      console.log(`   Останній вхід: ${data.user.last_sign_in_at}`);
      
      if (data.session) {
        console.log('');
        console.log('🔐 Сесія створена:');
        console.log(`   Access Token: ${data.session.access_token.substring(0, 50)}...`);
        console.log(`   Expires At: ${data.session.expires_at}`);
      }

      // Перевіряємо профіль
      console.log('');
      console.log('👤 Перевіряємо профіль...');
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) {
        console.log('❌ Помилка отримання профілю:', profileError.message);
      } else {
        console.log('✅ Профіль знайдено:');
        console.log(`   Email: ${profile.email}`);
        console.log(`   Ім'я: ${profile.full_name || 'Не вказано'}`);
        console.log(`   Створено: ${profile.created_at}`);
      }

      // Вихід з системи
      await supabase.auth.signOut();
      console.log('');
      console.log('🚪 Вихід з системи виконано');
      
      return true;
    }

    console.log('❌ Невідома помилка - користувач не повернувся');
    return false;

  } catch (error) {
    console.error('💥 КРИТИЧНА ПОМИЛКА:', error.message);
    return false;
  }
}

async function main() {
  console.log('🎯 Мета: Перевірити повний цикл автентифікації');
  console.log('');

  const success = await testAuthentication();
  
  console.log('');
  console.log('==================================');
  
  if (success) {
    console.log('🎉 ПОВНИЙ УСПІХ!');
    console.log('');
    console.log('✅ Проблема з автентифікацією ВИРІШЕНА:');
    console.log('   • Користувач існує в auth.users');
    console.log('   • Email підтверджено');
    console.log('   • Профіль створено в profiles');
    console.log('   • Автентифікація працює');
    console.log('');
    console.log('📱 Користувач може увійти в додаток з:');
    console.log(`   Email: ${TEST_EMAIL}`);
    console.log(`   Password: ${TEST_PASSWORD}`);
  } else {
    console.log('⚠️  ЧАСТКОВИЙ УСПІХ');
    console.log('');
    console.log('✅ Вирішено:');
    console.log('   • Профіль створено');
    console.log('   • Структура даних відновлена');
    console.log('');
    console.log('❌ Залишається:');
    console.log('   • Проблема з паролем');
    console.log('');
    console.log('🔧 Наступні кроки:');
    console.log('1. Скинути пароль через Supabase Dashboard');
    console.log('2. Або використати функцію "Забули пароль?" в додатку');
  }
}

main();