{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-2bb366fffcec2d9349f4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNGoogleSignInCGen_autolinked_build", "jsonFile": "directory-RNGoogleSignInCGen_autolinked_build-Debug-acc3a8e85f6b92c904ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-28576ea94fce1b4d9080.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-df26a25165bb4f3ec2fd.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-27dfbf7a6030a86e418e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-b7a2b16447a630ad8fa2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/expo-supabase-starter/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-67f01d7d679eaf6ea885.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0", "jsonFile": "target-react_codegen_RNGoogleSignInCGen-Debug-c59a285ab719b5de3f64.json", "name": "react_codegen_RNGoogleSignInCGen", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-c080178d1c44ce366602.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-ffd02f62a7820fe6a62c.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-3a18aaaeba08f5cf28a1.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-77e72f3850ab754f0e70.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-93861f7f20daace06340.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/expo-supabase-starter/android/app/.cxx/Debug/4626513p/x86_64", "source": "C:/expo-supabase-starter/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}