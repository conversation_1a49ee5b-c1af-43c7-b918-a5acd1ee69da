# Чеклист для застосування виправлень в продакшн

**Дата створення**: 28.06.2025  
**Мета**: Застосування виправлень реєстрації користувачів в продакшн середовищі

## 📋 Передумови

- [x] Проблема вирішена в локальному середовищі
- [x] Успішно протестована реєстрація користувача
- [x] Створена документація вирішення
- [ ] Готовність до застосування в продакшн

## 🚀 Кроки для продакшн деплойменту

### 1. Підготовка міграцій

- [ ] **Перевірити міграцію `007_restore_user_trigger.sql`**
  ```bash
  # Перевірити синтаксис SQL
  cat supabase/migrations/007_restore_user_trigger.sql
  ```

- [ ] **Створити бекап продакшн бази даних**
  ```bash
  # Через Supabase Dashboard → Settings → Database → Backups
  # Або через CLI (якщо налаштовано)
  supabase db dump --db-url "postgresql://..." > backup_before_fix.sql
  ```

### 2. Застосування міграцій в продакшн

- [ ] **Застосувати міграцію через Supabase Dashboard**
  1. Відкрити https://supabase.com/dashboard/project/xaeztaeqyjubmpgjxcgh
  2. Перейти до SQL Editor
  3. Виконати вміст файлу `007_restore_user_trigger.sql`

- [ ] **Перевірити створення тригера**
  ```sql
  -- Перевірити функцію
  SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';
  
  -- Перевірити тригер
  SELECT trigger_name FROM information_schema.triggers 
  WHERE trigger_name = 'on_auth_user_created';
  
  -- Перевірити RLS політики
  SELECT policyname FROM pg_policies WHERE tablename = 'profiles';
  ```

### 3. Оновлення конфігурації додатку

- [ ] **Перемкнути `.env.local` на продакшн**
  ```env
  # Розкоментувати продакшн налаштування
  EXPO_PUBLIC_SUPABASE_URL=https://xaeztaeqyjubmpgjxcgh.supabase.co
  EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  
  # Закоментувати локальні налаштування
  # EXPO_PUBLIC_SUPABASE_URL=http://10.0.2.2:54321
  ```

- [ ] **Перезапустити додаток**
  ```bash
  # Очистити кеш та перезапустити
  yarn start --clear
  ```

### 4. Тестування в продакшн

- [ ] **Протестувати реєстрацію нового користувача**
  - Використати тестовий email (наприклад, `<EMAIL>`)
  - Пройти повний flow: план → оплата → реєстрація
  - Перевірити створення профілю в Supabase Dashboard

- [ ] **Перевірити логи**
  ```bash
  # Через Supabase Dashboard → Logs
  # Шукати повідомлення:
  # "Creating profile for new user: ..."
  # "Profile created successfully for user: ..."
  ```

- [ ] **Перевірити таблицю profiles**
  ```sql
  SELECT id, email, name, subscription_type, created_at 
  FROM profiles 
  ORDER BY created_at DESC 
  LIMIT 5;
  ```

### 5. Моніторинг після деплойменту

- [ ] **Налаштувати алерти для помилок реєстрації**
  - Через Supabase Dashboard → Logs → Alerts
  - Відстежувати помилки типу "Database error saving new user"

- [ ] **Моніторити успішність реєстрацій**
  ```sql
  -- Статистика реєстрацій за останню годину
  SELECT 
    COUNT(*) as registrations,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as last_hour
  FROM auth.users;
  ```

## 🔧 Rollback план

### У разі проблем:

- [ ] **Відключити тригер**
  ```sql
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  ```

- [ ] **Відновити з бекапу**
  ```bash
  # Відновити з бекапу через Supabase Dashboard
  # Або через SQL:
  psql "postgresql://..." < backup_before_fix.sql
  ```

- [ ] **Повернути локальні налаштування**
  ```env
  EXPO_PUBLIC_SUPABASE_URL=http://10.0.2.2:54321
  ```

## 📊 Критерії успіху

- [ ] ✅ Нові користувачі можуть реєструватися без помилок
- [ ] ✅ Профілі створюються автоматично в таблиці `profiles`
- [ ] ✅ Метадані користувача (ім'я, план підписки) зберігаються правильно
- [ ] ✅ Навігація після реєстрації працює коректно
- [ ] ✅ Відсутні помилки в логах Supabase

## 📞 Контакти для підтримки

- **Документація**: `docs/AUTH_REGISTRATION_ISSUE_RESOLUTION.md`
- **Діагностичні скрипти**: `scripts/debug-auth-issue.js`
- **Supabase Dashboard**: https://supabase.com/dashboard/project/xaeztaeqyjubmpgjxcgh

---

**Примітка**: Цей чеклист створено на основі успішного вирішення проблеми в локальному середовищі. Всі кроки протестовані та підтверджені.
