import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { ActivityIndicator, View, ScrollView, Pressable } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import * as z from "zod";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormInput } from "@/components/ui/form";
import { Text } from "@/components/ui/text";
import { H1, H2, Muted } from "@/components/ui/typography";
import { useAuth } from "@/context/supabase-provider";
import { useColorScheme } from "@/lib/useColorScheme";
import { Checkbox } from "@/components/ui/checkbox";

const formSchema = z
  .object({
    name: z.string().min(2, "Будь ласка, введіть ваше ім'я."),
    email: z.string().email("Будь ласка, введіть дійсну email адресу."),
    password: z
      .string()
      .min(8, "Пароль має містити мінімум 8 символів.")
      .max(64, "Пароль має містити максимум 64 символи.")
      .regex(
        /^(?=.*[a-z])/,
        "Пароль має містити хоча б одну малу літеру.",
      )
      .regex(
        /^(?=.*[A-Z])/,
        "Пароль має містити хоча б одну велику літеру.",
      )
      .regex(/^(?=.*[0-9])/, "Пароль має містити хоча б одну цифру.")
      .regex(
        /^(?=.*[!@#$%^&*])/,
        "Пароль має містити хоча б один спеціальний символ.",
      ),
    confirmPassword: z.string().min(8, "Будь ласка, підтвердіть пароль."),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Паролі не співпадають.",
    path: ["confirmPassword"],
  });

export default function SignUp() {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const { signUp, signInWithGoogle, signInWithApple } = useAuth();
  const params = useLocalSearchParams();
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Отримуємо параметри плану з subscription-plans.tsx або PaymentScreen
  const selectedPlan = params.selectedPlan as string || "yearly";
  const planName = params.planName as string || "Річна підписка";
  const planPrice = params.planPrice as string || "799 грн";
  const planPeriod = params.planPeriod as string || "рік";
  const paymentCompleted = params.paymentCompleted as string === "true";

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  async function onSubmit(data: z.infer<typeof formSchema>) {
    if (!acceptTerms) {
      alert("Будь ласка, прийміть умови використання");
      return;
    }

    try {
      await signUp(data.email, data.password, {
        name: data.name,
        subscription_plan: selectedPlan,
      });

      if (paymentCompleted) {
        // Якщо оплата вже пройшла, переходимо до головного екрану
        alert("Реєстрація успішна! Ласкаво просимо до Розрахуй і В'яжи!");
        router.replace("/(protected)/calculators");
      } else {
        // Якщо оплата ще не пройшла, переходимо до оплати
        router.push({
          pathname: "/payment",
          params: {
            selectedPlan,
            planName,
            planPrice,
            planPeriod,
            email: data.email,
            name: data.name,
          },
        });
      }
    } catch (error: Error | any) {
      console.error(error.message);
      alert(error.message || "Помилка реєстрації");
    }
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-border">
          <Pressable onPress={() => router.back()}>
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={colorScheme === "dark" ? "#fff" : "#000"} 
            />
          </Pressable>
          <H2>Реєстрація</H2>
          <View style={{ width: 24 }} />
        </View>

        {/* Selected Plan Info */}
        <View className="bg-primary/10 p-4 m-4 rounded-xl">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-base font-medium">Обраний план:</Text>
            <Text className="text-base font-bold text-primary">{planName}</Text>
          </View>
          <View className="flex-row justify-between items-center">
            <Text className="text-sm text-muted-foreground">Вартість:</Text>
            <Text className="text-sm font-semibold text-primary">{planPrice}/{planPeriod}</Text>
          </View>
          {paymentCompleted && (
            <View className="mt-2 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-md self-start">
              <Text className="text-xs text-green-700 dark:text-green-300 font-medium">
                ✅ Оплата пройшла успішно
              </Text>
            </View>
          )}
          {selectedPlan === "yearly" && !paymentCompleted && (
            <View className="mt-2 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-md self-start">
              <Text className="text-xs text-green-700 dark:text-green-300 font-medium">
                Економія 33%
              </Text>
            </View>
          )}
        </View>

        {/* Form */}
        <View className="p-4">
          <Form {...form}>
            <View className="gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormInput
                    label="Ім'я"
                    placeholder="Ваше ім'я"
                    autoCapitalize="words"
                    autoComplete="name"
                    autoCorrect={false}
                    {...field}
                  />
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormInput
                    label="Email"
                    placeholder="<EMAIL>"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    keyboardType="email-address"
                    {...field}
                  />
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <View>
                    <FormInput
                      label="Пароль"
                      placeholder="Мінімум 8 символів"
                      autoCapitalize="none"
                      autoCorrect={false}
                      secureTextEntry={!showPassword}
                      {...field}
                    />
                    <Pressable
                      onPress={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-9"
                    >
                      <Ionicons
                        name={showPassword ? "eye-off" : "eye"}
                        size={20}
                        color={colorScheme === "dark" ? "#999" : "#666"}
                      />
                    </Pressable>
                  </View>
                )}
              />
              
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <View>
                    <FormInput
                      label="Підтвердити пароль"
                      placeholder="Повторіть пароль"
                      autoCapitalize="none"
                      autoCorrect={false}
                      secureTextEntry={!showConfirmPassword}
                      {...field}
                    />
                    <Pressable
                      onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-9"
                    >
                      <Ionicons
                        name={showConfirmPassword ? "eye-off" : "eye"}
                        size={20}
                        color={colorScheme === "dark" ? "#999" : "#666"}
                      />
                    </Pressable>
                  </View>
                )}
              />
            </View>
          </Form>

          {/* Terms and Conditions */}
          <View className="flex-row items-start gap-x-3 mt-6">
            <Checkbox
              checked={acceptTerms}
              onCheckedChange={setAcceptTerms}
              className="mt-1"
            />
            <Pressable onPress={() => setAcceptTerms(!acceptTerms)} className="flex-1">
              <Text className="text-sm text-muted-foreground">
                Я приймаю{" "}
                <Text 
                  className="text-primary underline"
                  onPress={() => router.push("/terms")}
                >
                  умови використання
                </Text>
                {" "}та{" "}
                <Text 
                  className="text-primary underline"
                  onPress={() => router.push("/privacy")}
                >
                  політику конфіденційності
                </Text>
              </Text>
            </Pressable>
          </View>

          {/* Social Auth */}
          <View className="mt-8">
            <View className="flex-row items-center gap-x-4 mb-6">
              <View className="flex-1 h-[1px] bg-border" />
              <Muted>або зареєструйтесь через</Muted>
              <View className="flex-1 h-[1px] bg-border" />
            </View>

            <View className="gap-y-3">
              <Button
                size="lg"
                variant="outline"
                onPress={async () => {
                  try {
                    await signInWithGoogle();
                  } catch (error) {
                    console.error("Google sign up error:", error);
                    alert("Помилка входу через Google");
                  }
                }}
                className="w-full"
              >
                <View className="flex-row items-center gap-x-3">
                  <Ionicons name="logo-google" size={20} color="#DB4437" />
                  <Text>Продовжити з Google</Text>
                </View>
              </Button>

              <Button
                size="lg"
                variant="outline"
                onPress={async () => {
                  try {
                    await signInWithApple();
                  } catch (error) {
                    console.error("Apple sign up error:", error);
                    alert("Помилка входу через Apple");
                  }
                }}
                className="w-full"
              >
                <View className="flex-row items-center gap-x-3">
                  <Ionicons name="logo-apple" size={20} color={colorScheme === "dark" ? "#fff" : "#000"} />
                  <Text>Продовжити з Apple</Text>
                </View>
              </Button>
            </View>
          </View>

          {/* Already have account */}
          <View className="flex-row items-center justify-center gap-x-2 mt-6 mb-4">
            <Muted>Вже маєте акаунт?</Muted>
            <Pressable onPress={() => router.push("/sign-in")}>
              <Text className="text-primary font-medium">Увійти</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View className="p-4 border-t border-border">
        <Button
          size="lg"
          variant="default"
          onPress={form.handleSubmit(onSubmit)}
          disabled={form.formState.isSubmitting || !acceptTerms}
          className="w-full"
        >
          {form.formState.isSubmitting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text>Продовжити до оплати</Text>
          )}
        </Button>
      </View>
    </SafeAreaView>
  );
}
