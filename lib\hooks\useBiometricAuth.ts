import { useState, useEffect } from 'react';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Alert } from 'react-native';

interface BiometricAuthResult {
  isAvailable: boolean;
  isEnrolled: boolean;
  biometricType: LocalAuthentication.AuthenticationType[];
  authenticate: () => Promise<boolean>;
  saveBiometricPreference: (enabled: boolean) => Promise<void>;
  getBiometricPreference: () => Promise<boolean>;
  saveCredentials: (email: string, password: string) => Promise<void>;
  getCredentials: () => Promise<{ email: string; password: string } | null>;
  clearCredentials: () => Promise<void>;
}

const BIOMETRIC_PREFERENCE_KEY = 'biometric_auth_enabled';
const CREDENTIALS_EMAIL_KEY = 'biometric_auth_email';
const CREDENTIALS_PASSWORD_KEY = 'biometric_auth_password';

export const useBiometricAuth = (): BiometricAuthResult => {
  const [isAvailable, setIsAvailable] = useState(false);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [biometricType, setBiometricType] = useState<LocalAuthentication.AuthenticationType[]>([]);

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      setIsAvailable(compatible);

      if (compatible) {
        const enrolled = await LocalAuthentication.isEnrolledAsync();
        setIsEnrolled(enrolled);

        const types = await LocalAuthentication.supportedAuthenticationTypesAsync();
        setBiometricType(types);
      }
    } catch (error) {
      console.error('Error checking biometric availability:', error);
    }
  };

  const authenticate = async (): Promise<boolean> => {
    if (!isAvailable || !isEnrolled) {
      return false;
    }

    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Увійдіть за допомогою біометрії',
        cancelLabel: 'Скасувати',
        fallbackLabel: 'Використати пароль',
        disableDeviceFallback: false,
      });

      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  };

  const saveBiometricPreference = async (enabled: boolean): Promise<void> => {
    try {
      await SecureStore.setItemAsync(BIOMETRIC_PREFERENCE_KEY, enabled.toString());
    } catch (error) {
      console.error('Error saving biometric preference:', error);
      Alert.alert('Помилка', 'Не вдалося зберегти налаштування біометрії');
    }
  };

  const getBiometricPreference = async (): Promise<boolean> => {
    try {
      const value = await SecureStore.getItemAsync(BIOMETRIC_PREFERENCE_KEY);
      return value === 'true';
    } catch (error) {
      console.error('Error getting biometric preference:', error);
      return false;
    }
  };

  const saveCredentials = async (email: string, password: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(CREDENTIALS_EMAIL_KEY, email);
      await SecureStore.setItemAsync(CREDENTIALS_PASSWORD_KEY, password);
    } catch (error) {
      console.error('Error saving credentials:', error);
      Alert.alert('Помилка', 'Не вдалося зберегти дані для біометричного входу');
    }
  };

  const getCredentials = async (): Promise<{ email: string; password: string } | null> => {
    try {
      const email = await SecureStore.getItemAsync(CREDENTIALS_EMAIL_KEY);
      const password = await SecureStore.getItemAsync(CREDENTIALS_PASSWORD_KEY);

      if (email && password) {
        return { email, password };
      }
      return null;
    } catch (error) {
      console.error('Error getting credentials:', error);
      return null;
    }
  };

  const clearCredentials = async (): Promise<void> => {
    try {
      await SecureStore.deleteItemAsync(CREDENTIALS_EMAIL_KEY);
      await SecureStore.deleteItemAsync(CREDENTIALS_PASSWORD_KEY);
      await SecureStore.deleteItemAsync(BIOMETRIC_PREFERENCE_KEY);
    } catch (error) {
      console.error('Error clearing credentials:', error);
    }
  };

  return {
    isAvailable,
    isEnrolled,
    biometricType,
    authenticate,
    saveBiometricPreference,
    getBiometricPreference,
    saveCredentials,
    getCredentials,
    clearCredentials,
  };
};