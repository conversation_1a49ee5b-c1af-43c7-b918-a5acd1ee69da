import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { merchantAccount, orderReference, amount, currency, authCode, 
            cardPan, transactionStatus, reasonCode, merchantSignature } = await req.json()

    // Verify signature
    const secretKey = Deno.env.get('WAYFORPAY_SECRET_KEY')!
    const signString = `${merchantAccount};${orderReference};${amount};${currency};${authCode};${cardPan};${transactionStatus};${reasonCode}`
    
    const encoder = new TextEncoder()
    const data = encoder.encode(signString)
    const key = encoder.encode(secretKey)
    
    const signature = await crypto.subtle.sign(
      "HMAC",
      await crypto.subtle.importKey(
        "raw",
        key,
        { name: "HMAC", hash: "SHA-1" },
        false,
        ["sign"]
      ),
      data
    )
    
    const calculatedSignature = btoa(String.fromCharCode(...new Uint8Array(signature)))
    
    if (calculatedSignature !== merchantSignature) {
      throw new Error('Invalid signature')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Process payment based on status
    if (transactionStatus === 'Approved') {
      // Update user subscription
      const userId = orderReference.split('_')[1] // Assuming format: order_userId_timestamp
      
      const { error } = await supabase
        .from('profiles')
        .update({ 
          subscription_type: 'premium',
          subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        })
        .eq('id', userId)
      
      if (error) throw error
      
      // Log successful payment
      await supabase.from('payment_logs').insert({
        user_id: userId,
        order_reference: orderReference,
        amount,
        currency,
        status: 'success',
        provider: 'wayforpay',
        metadata: { authCode, cardPan }
      })
    }

    // Return success response to Wayforpay
    return new Response(JSON.stringify({
      orderReference,
      status: 'accept',
      time: Date.now(),
      signature: calculatedSignature
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Payment webhook error:', error)
    
    return new Response(JSON.stringify({
      status: 'error',
      message: error.message
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})