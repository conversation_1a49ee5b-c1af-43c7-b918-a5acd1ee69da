@echo off
echo ========================================
echo ПОВНЕ ОЧИЩЕННЯ КЕШІВ REACT NATIVE/EXPO
echo Проєкт: "Розрахуй і В'яжи"
echo Дата: %date% %time%
echo ========================================

echo.
echo [1/10] Зупинка процесів...
echo ----------------------------------------
echo Завершення Node.js процесів...
taskkill /F /IM node.exe 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js процеси завершені
) else (
    echo ⚠️ Node.js процеси не були активні
)

echo Завершення Java процесів...
taskkill /F /IM java.exe 2>nul
if %errorlevel% equ 0 (
    echo ✅ Java процеси завершені
) else (
    echo ⚠️ Java процеси не були активні
)

echo.
echo [2/10] Перезапуск ADB...
echo ----------------------------------------
adb kill-server
timeout /t 2 >nul
adb start-server
echo ✅ ADB перезапущений

echo.
echo [3/10] Очищення Metro кешу...
echo ----------------------------------------
if exist "node_modules" (
    echo Очищення Metro кешу через npx...
    npx react-native start --reset-cache --verbose >nul 2>&1
    echo ✅ Metro кеш очищений
) else (
    echo ⚠️ node_modules не знайдено, пропускаємо Metro кеш
)

echo.
echo [4/10] Очищення Expo кешу...
echo ----------------------------------------
echo Очищення Expo кешу...
npx expo start --clear >nul 2>&1
echo ✅ Expo кеш очищений

echo.
echo [5/10] Очищення Yarn кешу...
echo ----------------------------------------
echo Очищення yarn кешу...
yarn cache clean >nul 2>&1
echo ✅ Yarn кеш очищений

echo.
echo [6/10] Очищення Watchman кешу...
echo ----------------------------------------
where watchman >nul 2>&1
if %errorlevel% equ 0 (
    echo Очищення Watchman кешу...
    watchman watch-del-all >nul 2>&1
    echo ✅ Watchman кеш очищений
) else (
    echo ⚠️ Watchman не встановлений, пропускаємо
)

echo.
echo [7/10] Очищення Android build...
echo ----------------------------------------
if exist "android" (
    cd android
    echo Виконання gradlew clean...
    call gradlew clean >nul 2>&1
    echo Виконання gradlew cleanBuildCache...
    call gradlew cleanBuildCache >nul 2>&1
    cd ..
    echo ✅ Android build очищений
) else (
    echo ⚠️ Папка android не знайдена
)

echo.
echo [8/10] Видалення build папок...
echo ----------------------------------------
if exist "android\app\build" (
    rmdir /s /q android\app\build 2>nul
    echo ✅ android\app\build видалено
) else (
    echo ⚠️ android\app\build не існує
)

if exist "android\build" (
    rmdir /s /q android\build 2>nul
    echo ✅ android\build видалено
) else (
    echo ⚠️ android\build не існує
)

echo.
echo [9/10] Очищення глобальних кешів...
echo ----------------------------------------
echo Очищення глобального Gradle кешу...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
    echo ✅ Gradle кеш очищений
) else (
    echo ⚠️ Gradle кеш не знайдений
)

echo Очищення npm кешу...
npm cache clean --force >nul 2>&1
echo ✅ NPM кеш очищений

echo.
echo [10/10] Перевірка емулятора...
echo ----------------------------------------
echo Поточний стан емулятора:
adb devices -l
echo.

echo ========================================
echo ОЧИЩЕННЯ ЗАВЕРШЕНО!
echo ========================================
echo.
echo 📋 Наступні кроки:
echo 1. Перезапустіть емулятор з Cold Boot:
echo    - Android Studio → AVD Manager
echo    - Dropdown → Cold Boot Now
echo.
echo 2. Запустіть додаток з чистими кешами:
echo    npx expo start --clear --android
echo.
echo 3. Альтернативні команди запуску:
echo    - yarn start --clear
echo    - npx expo start --clear --localhost --android
echo    - npx expo start --clear --tunnel --android
echo.
echo 4. Моніторинг логів:
echo    adb logcat ^| findstr -i "expo\|react\|metro\|error"
echo.
echo ⚠️ Якщо проблеми залишаються:
echo - Створіть новий AVD
echo - Перевстановіть node_modules: rmdir /s /q node_modules ^&^& yarn install
echo - Спробуйте на фізичному пристрої
echo.
pause