# Налагодження локального середовища Docker та Supabase

Цей документ містить інструкції для діагностики та вирішення поширених проблем з локальним середовищем розробки Supabase, що працює на Docker.

## Симптоми проблем

Ви можете зіткнутися з однією або декількома з наступних проблем:

1.  **Помилки під час виконання команд `supabase`**: Команди завершуються з помилками, пов'язаними з базою даних або автентифікацією (наприклад, `Database error saving new user`).
2.  **Контейнери постійно перезапускаються**: `docker ps` показує статус `Restarting` для одного або декількох контейнерів (наприклад, `supabase_vector_...`).
3.  **Зупинені контейнери**: Один або кілька критично важливих контейнерів мають статус `Exited` (наприклад, `supabase_edge_runtime_...`).
4.  **Мережеві помилки в логах**: Логи контейнерів містять помилки `Network unreachable (os error 101)` або `Connection refused (os error 111)`.

## Алгоритм діагностики та виправлення

Виконуйте ці кроки послідовно, щоб знайти та усунути проблему.

### Крок 1: Перевірка статусу контейнерів

Запустіть команду, щоб побачити стан усіх контейнерів:

```bash
docker ps -a
```

-   **Що шукати:** Зверніть увагу на колонку `STATUS`. Всі контейнери повинні мати статус `Up (healthy)` або просто `Up`. Якщо ви бачите `Restarting` або `Exited`, це вказує на проблему.

### Крок 2: Аналіз логів проблемного контейнера

Якщо якийсь контейнер не працює належним чином, перегляньте його логи, щоб знайти причину.

```bash
# Замініть <container_id_or_name> на ID або ім'я проблемного контейнера
docker logs <container_id_or_name>
```

-   **Що шукати:** Шукайте повідомлення про помилки (`ERROR`, `failed`, `panic`), які можуть вказати на причину збою.

### Крок 3: Повний перезапуск середовища Supabase (Найбільш часте рішення)

Найчастіше проблеми з локальним середовищем Docker вирішуються повним "чистим" перезапуском.

**Важливо:** Переконайтеся, що у вас встановлено Supabase CLI. Якщо ні, виконайте інструкції з Кроку 4.

1.  **Зупинити та видалити всі контейнери:**
    Ця команда повністю очистить ваше локальне середовище. **Локальні дані в базі даних буде втрачено.**

    ```bash
    supabase stop --no-backup
    ```

2.  **Запустити середовище з нуля:**

    ```bash
    supabase start
    ```

3.  **Перевірити статус знову:**
    Після запуску знову виконайте `docker ps` і переконайтеся, що всі критично важливі сервіси (особливо `edge-runtime`, `postgres`, `gotrue`) працюють. Проблеми з `vector` на даному етапі можна ігнорувати.

### Крок 4: Встановлення або перевстановлення Supabase CLI

Якщо команда `supabase` не розпізнана, вам потрібно встановити CLI. Найкращий спосіб для Windows — через пакетний менеджер **Scoop**.

1.  **Встановити Scoop (якщо ще не встановлено):**
    *   Дозволити виконання скриптів:
        ```powershell
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
        ```
    *   Встановити Scoop:
        ```powershell
        irm get.scoop.sh | iex
        ```

2.  **Встановити Supabase CLI через Scoop:**
    ```powershell
    scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
    scoop install supabase
    ```

Після цього команда `supabase` повинна стати доступною, і ви можете повернутися до Кроку 3.

### Крок 5: Якщо нічого не допомогло

Якщо навіть після повного перезапуску проблеми залишаються, це може вказувати на більш глибокі проблеми з конфігурацією Docker Desktop або WSL на вашій системі. Розгляньте наступні дії:
- Перезавантажте Docker Desktop.
- Перевірте налаштування мережі в Docker Desktop.
- Перезавантажте комп'ютер.