# Налаштування Google OAuth для "Розрахуй і В'яжи"

## 1. Налаштування в Supabase Dashboard

### Крок 1: Відкрийте Supabase Dashboard
1. Перейдіть на https://app.supabase.com
2. Оберіть ваш проєкт
3. Перейдіть до **Authentication** → **Providers**

### Крок 2: Увімкніть Google Provider
1. Знайдіть **Google** в списку провайдерів
2. Натисніть **Enable Google**
3. Заповніть поля:

#### Client ID (for OAuth)
```
443512468984-ulck6pfk038lr7aang9v9g0brdbu0pj6.apps.googleusercontent.com
```

#### Client Secret (for OAuth)
```
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
```

4. **Authorized Client IDs** (додайте всі три):
```
443512468984-ost3g20nqh0v1ght668fvmjoi1auh2a7.apps.googleusercontent.com
443512468984-6u4cfa6v4j3b9e4ck4e6ktp8924hi0b3.apps.googleusercontent.com
443512468984-ulck6pfk038lr7aang9v9g0brdbu0pj6.apps.googleusercontent.com
```

5. Натисніть **Save**

### Крок 3: Скопіюйте Redirect URL
1. Після збереження з'явиться **Redirect URL**
2. Скопіюйте його (виглядає як: `https://[your-project-ref].supabase.co/auth/v1/callback`)

## 2. Налаштування в Google Cloud Console

### Крок 1: Додайте Redirect URI
1. Перейдіть на https://console.cloud.google.com
2. Оберіть ваш проєкт
3. Перейдіть до **APIs & Services** → **Credentials**
4. Знайдіть **OAuth 2.0 Client IDs**
5. Відредагуйте **Web client**

### Крок 2: Додайте Authorized redirect URIs
Додайте наступні URI:
- Redirect URL з Supabase (скопійований вище)
- `http://localhost:19006` (для локальної розробки)
- `https://auth.expo.io/@your-username/your-app-slug` (для Expo)

### Крок 3: Збережіть зміни

## 3. Оновлення конфігурації додатку

### Оновіть app.json для deep linking:
```json
{
  "expo": {
    "scheme": "knittingapp",
    "android": {
      "intentFilters": [
        {
          "action": "VIEW",
          "autoVerify": true,
          "data": [
            {
              "scheme": "knittingapp",
              "host": "*"
            }
          ],
          "category": ["BROWSABLE", "DEFAULT"]
        }
      ]
    },
    "ios": {
      "associatedDomains": [
        "applinks:your-project-ref.supabase.co"
      ]
    }
  }
}
```

## 4. Виконання SQL міграцій

### Через Supabase Dashboard:
1. Перейдіть до **SQL Editor**
2. Створіть новий запит
3. Скопіюйте вміст файлу `supabase/migrations/001_create_profiles_table.sql`
4. Виконайте запит

### Через Supabase CLI:
```bash
supabase db push
```

## 5. Тестування

### Локальне тестування:
```bash
# Запустіть додаток
yarn expo start

# Відкрийте на пристрої або емуляторі
# Спробуйте увійти через Google
```

### Перевірка:
1. ✅ Кнопка "Увійти через Google" відкриває браузер
2. ✅ Після авторизації повертає в додаток
3. ✅ Профіль користувача створюється автоматично
4. ✅ Сесія зберігається в SecureStore

## Важливі примітки

### Для Android:
- SHA-1 fingerprint вже налаштований для Client ID
- Переконайтеся, що package name співпадає: `com.knittingapp.calculator`

### Для iOS:
- Bundle ID має бути: `com.knittingapp.calculator`
- URL scheme налаштований в app.json

### Для Web:
- Authorized JavaScript origins мають включати ваш домен
- Redirect URIs мають включати callback URL

## Troubleshooting

### Помилка "redirect_uri_mismatch":
- Перевірте, що всі redirect URI додані в Google Console
- Переконайтеся, що використовуєте правильний Client ID

### Помилка "invalid_client":
- Перевірте Client ID та Client Secret
- Переконайтеся, що Google provider увімкнений в Supabase

### Користувач не створюється в profiles:
- Перевірте, що SQL міграція виконана
- Перевірте логи тригера в Supabase Dashboard

---

**Створено**: 21.06.2025  
**Для проєкту**: "Розрахуй і В'яжи"