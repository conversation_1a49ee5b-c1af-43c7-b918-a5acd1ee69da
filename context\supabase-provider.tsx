import {
	createContext,
	PropsWithChildren,
	useContext,
	useEffect,
	useState,
} from "react";
import { SplashScreen, useRouter } from "expo-router";


import { supabase } from "@/config/supabase";
import { MobileAuthService } from "@/lib/services/mobile-auth";
import { isMobilePlatform } from "@/lib/utils";
import { LoadingScreen } from "@/components/ui/LoadingScreen";

SplashScreen.preventAutoHideAsync();

type AuthState = {
	initialized: boolean;
	session: any | null;
	signUp: (
		email: string,
		password: string,
		options?: { data: { [key: string]: any } }
	) => Promise<void>;
	signIn: (email: string, password: string) => Promise<void>;
	signOut: () => Promise<void>;
	resetPassword: (email: string) => Promise<void>;
	signInWithGoogle: () => Promise<void>;
	signInWithApple: () => Promise<void>;
};

export const AuthContext = createContext<AuthState>({
	initialized: false,
	session: null,
	signUp: async () => {},
	signIn: async () => {},
	signOut: async () => {},
	resetPassword: async () => {},
	signInWithGoogle: async () => {},
	signInWithApple: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export function AuthProvider({ children }: PropsWithChildren) {
	const [initialized, setInitialized] = useState(false);
	const [session, setSession] = useState<any | null>(null);
	const [hasNavigated, setHasNavigated] = useState(false);
	const router = useRouter();

	const signUp = async (
		email: string,
		password: string,
		options?: { [key: string]: any }
	) => {
		console.log("🚀 Starting signUp with:", { email, options });

		const { data, error } = await supabase.auth.signUp({
			email,
			password,
			options: options ? { data: options } : undefined,
		});

		console.log("📊 SignUp response:", { data, error });

		if (error) {
			console.error("❌ Error signing up:", error);
			throw error; // ✅ ВИПРАВЛЕННЯ: викидаємо помилку
		}

		if (data.session && data.user) {
			setSession(data.session);
			console.log("User signed up:", data.user);

			// Fallback: создаем профиль если триггер не сработал
			try {
				const { error: profileError } = await supabase
					.from('profiles')
					.select('id')
					.eq('id', data.user.id)
					.single();

				if (profileError && profileError.code === 'PGRST116') {
					// Профиль не найден, создаем вручную
					console.log("Creating profile manually as fallback");
					await supabase
						.from('profiles')
						.insert({
							id: data.user.id,
							email: data.user.email!,
							name: options?.name || data.user.email!.split('@')[0],
							subscription_type: 'free'
						});
					console.log("Profile created manually");
				}
			} catch (fallbackError) {
				console.warn("Fallback profile creation failed:", fallbackError);
			}
		} else {
			console.log("No user returned from sign up");
		}
	};

	const signIn = async (email: string, password: string) => {
		const { data, error } = await supabase.auth.signInWithPassword({
			email,
			password,
		});

		if (error) {
			console.error("Error signing in:", error);
			throw error; // ✅ ВИПРАВЛЕННЯ: викидаємо помилку
		}

		if (data.session) {
			setSession(data.session);
			console.log("User signed in:", data.user);
		} else {
			console.log("No user returned from sign in");
		}
	};

	const signOut = async () => {
		const { error } = await supabase.auth.signOut();

		if (error) {
			console.error("Error signing out:", error);
			return;
		} else {
			console.log("User signed out");
		}
	};

	const resetPassword = async (email: string) => {
		const { error } = await supabase.auth.resetPasswordForEmail(email, {
			redirectTo: 'com.knittingapp.calculator://reset-password',
		});

		if (error) {
			console.error("Error resetting password:", error);
			throw error;
		} else {
			console.log("Password reset email sent");
		}
	};

	const signInWithGoogle = async () => {
		try {
			console.log("🚀 Initiating Google OAuth sign-in...");
			const { error } = await supabase.auth.signInWithOAuth({
				provider: 'google',
				options: {
					// `redirectTo` не потрібен для мобільних додатків,
					// оскільки Supabase автоматично використовує deep link.
					// Схема URL вже налаштована в app.json.
				},
			});

			if (error) {
				console.error("❌ Error during Google OAuth sign-in:", error.message);
				throw error;
			}
			console.log("✅ Google OAuth process started successfully.");
		} catch (error) {
			console.error("❌ An unexpected error occurred during Google sign-in:", error);
			throw error;
		}
	};

	const signInWithApple = async () => {
		const { error } = await supabase.auth.signInWithOAuth({
			provider: 'apple',
			options: {
				redirectTo: 'com.knittingapp.calculator://auth/callback',
			},
		});

		if (error) {
			console.error("Error signing in with Apple:", error);
			throw error;
		}
	};

	useEffect(() => {
		let authListener: any;

		const initializeAuth = async () => {
			console.log("🚀 AuthProvider: Initializing auth...");
			
			// Отримуємо початкову сесію
			const { data: { session } } = await supabase.auth.getSession();
			console.log("🔍 AuthProvider: Initial session:", { hasSession: !!session });
			setSession(session);

			// Налаштовуємо listener для змін автентифікації
			const { data: { subscription } } = supabase.auth.onAuthStateChange((_event: any, session: any) => {
				console.log("🔍 AuthProvider: Auth state changed:", { event: _event, hasSession: !!session });
				setSession(session);
				
				// ✅ ВИПРАВЛЕННЯ: Скидаємо hasNavigated при зміні стану автентифікації
				// щоб дозволити навігацію після входу/виходу
				if (_event === 'SIGNED_IN' || _event === 'SIGNED_OUT' || _event === 'TOKEN_REFRESHED') {
					console.log("🔄 Resetting hasNavigated due to auth state change:", _event);
					setHasNavigated(false);
				}
			});

			authListener = subscription;
			setInitialized(true);
			console.log("✅ AuthProvider: Initialization complete");
		};

		initializeAuth();

		// Cleanup listener при unmount
		return () => {
			if (authListener) {
				console.log("🧹 AuthProvider: Cleaning up auth listener");
				authListener.unsubscribe();
			}
		};
	}, []);

	useEffect(() => {
		console.log("🔍 Navigation useEffect triggered:", {
			initialized,
			session: !!session,
			hasNavigated,
			sessionUser: session?.user?.email
		});
		
		if (initialized && !hasNavigated) {
			console.log("🚀 Starting navigation logic...");
			SplashScreen.hideAsync();
			
			if (session) {
				console.log("✅ User authenticated, navigating to calculators screen");
				console.log("📧 User email:", session.user?.email);
				console.log("🎯 Navigating to: /(protected)/calculators");
				router.replace("/(protected)/calculators");
				setHasNavigated(true);
				console.log("✅ Navigation completed, hasNavigated set to true");
			} else {
				console.log("❌ No session, navigating to welcome screen");
				console.log("🎯 Navigating to: /welcome");
				router.replace("/welcome");
				setHasNavigated(true);
				console.log("✅ Navigation completed, hasNavigated set to true");
			}
		} else {
			console.log("⏸️ Navigation skipped:", {
				reason: !initialized ? "not initialized" : hasNavigated ? "already navigated" : "unknown"
			});
		}
	}, [initialized, session, hasNavigated, router]);

	// Показуємо екран завантаження поки ініціалізується автентифікація
	if (!initialized) {
		return <LoadingScreen message="Ініціалізація додатку..." />
	}

	return (
		<AuthContext.Provider
			value={{
				initialized,
				session,
				signUp,
				signIn,
				signOut,
				resetPassword,
				signInWithGoogle,
				signInWithApple,
			}}
		>
			{children}
		</AuthContext.Provider>
	);
}
