import React, { useState } from "react";
import { View, ScrollView, TextInput, Alert } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/context/supabase-provider";
import { Button } from "../../components/ui/button";
import { Text } from "../../components/ui/text";

export default function PaymentScreen() {
  const router = useRouter();
  const { session } = useAuth();
  const params = useLocalSearchParams();
  const { plan = "monthly", price = "199" } = params;

  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [cardholderName, setCardholderName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\s/g, "");
    const chunks = cleaned.match(/.{1,4}/g) || [];
    return chunks.join(" ").substr(0, 19);
  };

  const formatExpiryDate = (text: string) => {
    const cleaned = text.replace(/\D/g, "");
    if (cleaned.length >= 2) {
      return cleaned.slice(0, 2) + "/" + cleaned.slice(2, 4);
    }
    return cleaned;
  };

  const handlePayment = async () => {
    // Validate inputs
    if (!cardNumber || !expiryDate || !cvv || !cardholderName) {
      Alert.alert("Помилка", "Будь ласка, заповніть всі поля");
      return;
    }

    setIsProcessing(true);

    // TODO: Implement WayForPay payment processing
    // For now, simulate payment success
    setTimeout(() => {
      setIsProcessing(false);

      if (!session) {
        // Якщо немає сесії, після успішної оплати переходимо до реєстрації
        Alert.alert(
          "Оплата успішна!",
          "Тепер створіть акаунт для доступу до всіх функцій",
          [
            {
              text: "Створити акаунт",
              onPress: () => {
                router.push({
                  pathname: "/sign-up",
                  params: {
                    selectedPlan: plan,
                    planName: plan === "yearly" ? "Річна підписка" : "Місячна підписка",
                    planPrice: `${price} грн`,
                    planPeriod: plan === "yearly" ? "рік" : "місяць",
                    paymentCompleted: "true", // Позначаємо, що оплата вже пройшла
                  },
                });
              },
            },
          ]
        );
      } else {
        // Якщо є сесія, просто переходимо до головного екрану
        Alert.alert(
          "Успіх!",
          "Оплата пройшла успішно. Ласкаво просимо до Розрахуй і В'яжи!",
          [
            {
              text: "OK",
              onPress: () => {
                router.replace("/(protected)/home");
              },
            },
          ]
        );
      }
    }, 2000);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-row items-center justify-between px-4 py-2 border-b border-gray-200">
        <Button
          variant="ghost"
          size="icon"
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </Button>
        <Text className="text-lg font-semibold text-gray-900">
          Оплата підписки
        </Text>
        <View className="w-10" />
      </View>

      <ScrollView className="flex-1 px-4 py-6">
        {/* Order Summary */}
        <View className="bg-gray-50 rounded-lg p-4 mb-6">
          <Text className="text-base font-semibold text-gray-900 mb-2">
            Деталі замовлення
          </Text>
          <View className="flex-row justify-between mb-1">
            <Text className="text-sm text-gray-600">План:</Text>
            <Text className="text-sm font-medium text-gray-900">
              {plan === "yearly" ? "Річна підписка" : "Місячна підписка"}
            </Text>
          </View>
          <View className="flex-row justify-between">
            <Text className="text-sm text-gray-600">Сума:</Text>
            <Text className="text-sm font-medium text-gray-900">
              {price} грн
            </Text>
          </View>
        </View>

        {/* Payment Form */}
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Дані картки
        </Text>

        <View className="mb-4">
          <Text className="text-sm font-medium text-gray-700 mb-1">
            Номер картки
          </Text>
          <TextInput
            className="border border-gray-300 rounded-lg px-3 py-2 text-base"
            placeholder="1234 5678 9012 3456"
            value={cardNumber}
            onChangeText={(text) => setCardNumber(formatCardNumber(text))}
            keyboardType="numeric"
            maxLength={19}
          />
        </View>

        <View className="flex-row mb-4">
          <View className="flex-1 mr-2">
            <Text className="text-sm font-medium text-gray-700 mb-1">
              Термін дії
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-base"
              placeholder="MM/YY"
              value={expiryDate}
              onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
              keyboardType="numeric"
              maxLength={5}
            />
          </View>
          <View className="flex-1 ml-2">
            <Text className="text-sm font-medium text-gray-700 mb-1">
              CVV
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-base"
              placeholder="123"
              value={cvv}
              onChangeText={setCvv}
              keyboardType="numeric"
              maxLength={3}
              secureTextEntry
            />
          </View>
        </View>

        <View className="mb-6">
          <Text className="text-sm font-medium text-gray-700 mb-1">
            Ім'я власника картки
          </Text>
          <TextInput
            className="border border-gray-300 rounded-lg px-3 py-2 text-base"
            placeholder="Іван Іваненко"
            value={cardholderName}
            onChangeText={setCardholderName}
            autoCapitalize="words"
          />
        </View>

        {/* Security Notice */}
        <View className="flex-row items-start bg-blue-50 rounded-lg p-3 mb-6">
          <Ionicons name="lock-closed" size={20} color="#3b82f6" />
          <Text className="text-sm text-blue-800 ml-2 flex-1">
            Ваші платіжні дані захищені за допомогою шифрування. Ми не зберігаємо
            дані вашої картки.
          </Text>
        </View>

        {/* Payment Button */}
        <Button
          onPress={handlePayment}
          disabled={isProcessing}
          className="w-full"
        >
          <Text className="text-white font-semibold">
            {isProcessing ? "Обробка..." : `Оплатити ${price} грн`}
          </Text>
        </Button>

        {/* Alternative Payment Methods */}
        <View className="mt-6">
          <Text className="text-sm text-gray-600 text-center mb-3">
            Або оплатіть за допомогою
          </Text>
          <View className="flex-row justify-center space-x-4">
            <Button
              variant="outline"
              onPress={() => Alert.alert("Google Pay", "Скоро буде доступно")}
              className="flex-1 mr-2"
            >
              <View className="flex-row items-center justify-center">
                <Ionicons name="logo-google" size={20} color="#4285f4" />
                <Text className="ml-2">Google Pay</Text>
              </View>
            </Button>
            <Button
              variant="outline"
              onPress={() => Alert.alert("Apple Pay", "Скоро буде доступно")}
              className="flex-1 ml-2"
            >
              <View className="flex-row items-center justify-center">
                <Ionicons name="logo-apple" size={20} color="#000000" />
                <Text className="ml-2">Apple Pay</Text>
              </View>
            </Button>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}