/**
 * Прямий SQL запит до Supabase для створення профілю
 */

const https = require('https');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔧 ПРЯМИЙ SQL ЗАПИТ ДО SUPABASE');
console.log('=' .repeat(40));

async function executeDirectSQL(sql) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({ query: sql });
        
        const options = {
            hostname: supabaseUrl.replace('https://', '').replace('http://', ''),
            port: 443,
            path: '/rest/v1/rpc/exec_sql',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bear<PERSON> ${serviceKey}`,
                'apikey': serviceKey,
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve({ status: res.statusCode, data: result });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', (e) => {
            reject(e);
        });

        req.write(postData);
        req.end();
    });
}

async function createProfileDirectly() {
    console.log('\n👤 Створення профілю через прямий SQL...');
    
    const USER_ID = 'f15eb141-46b2-4b87-b5f0-5542f60b27a5';
    const TARGET_EMAIL = '<EMAIL>';
    
    // SQL для створення профілю
    const createProfileSQL = `
        INSERT INTO public.profiles (id, email, name, subscription_type, created_at, updated_at)
        VALUES (
            '${USER_ID}',
            '${TARGET_EMAIL}',
            'Public Relations',
            'free',
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            name = EXCLUDED.name,
            updated_at = NOW();
    `;
    
    try {
        console.log('🔧 Виконуємо SQL запит...');
        const result = await executeDirectSQL(createProfileSQL);
        
        console.log(`📊 Статус відповіді: ${result.status}`);
        
        if (result.status === 200 || result.status === 201) {
            console.log('✅ Профіль створений успішно!');
            return true;
        } else {
            console.log('❌ Помилка створення профілю:');
            console.log(JSON.stringify(result.data, null, 2));
            return false;
        }
        
    } catch (error) {
        console.log('❌ Помилка виконання запиту:', error.message);
        return false;
    }
}

async function verifyProfile() {
    console.log('\n🔍 Перевірка створеного профілю...');
    
    const USER_ID = 'f15eb141-46b2-4b87-b5f0-5542f60b27a5';
    
    const selectSQL = `
        SELECT id, email, name, subscription_type, created_at
        FROM public.profiles 
        WHERE id = '${USER_ID}';
    `;
    
    try {
        const result = await executeDirectSQL(selectSQL);
        
        if (result.status === 200 && result.data && result.data.length > 0) {
            const profile = result.data[0];
            console.log('✅ Профіль знайдено:');
            console.log(`   🆔 ID: ${profile.id}`);
            console.log(`   📧 Email: ${profile.email}`);
            console.log(`   👤 Ім'я: ${profile.name}`);
            console.log(`   💳 Підписка: ${profile.subscription_type}`);
            return true;
        } else {
            console.log('❌ Профіль не знайдено');
            console.log('📊 Відповідь сервера:', JSON.stringify(result.data, null, 2));
            return false;
        }
        
    } catch (error) {
        console.log('❌ Помилка перевірки:', error.message);
        return false;
    }
}

async function createTableIfNeeded() {
    console.log('\n🏗️ Створення таблиці profiles якщо потрібно...');
    
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.profiles (
            id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
            email TEXT UNIQUE NOT NULL,
            name TEXT,
            avatar_url TEXT,
            subscription_type TEXT DEFAULT 'free',
            subscription_expires_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Увімкнення RLS
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
        
        -- Створення політик якщо не існують
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_policies 
                WHERE tablename = 'profiles' AND policyname = 'Users can view own profile'
            ) THEN
                CREATE POLICY "Users can view own profile" ON public.profiles
                    FOR SELECT USING (auth.uid() = id);
            END IF;
            
            IF NOT EXISTS (
                SELECT 1 FROM pg_policies 
                WHERE tablename = 'profiles' AND policyname = 'Users can update own profile'
            ) THEN
                CREATE POLICY "Users can update own profile" ON public.profiles
                    FOR UPDATE USING (auth.uid() = id);
            END IF;
        END
        $$;
        
        -- Створення індексів
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
        CREATE INDEX IF NOT EXISTS idx_profiles_subscription_type ON public.profiles(subscription_type);
    `;
    
    try {
        const result = await executeDirectSQL(createTableSQL);
        
        if (result.status === 200 || result.status === 201) {
            console.log('✅ Таблиця та політики створені/оновлені');
            return true;
        } else {
            console.log('⚠️ Можлива помилка створення таблиці:');
            console.log(JSON.stringify(result.data, null, 2));
            return true; // Продовжуємо, можливо таблиця вже існує
        }
        
    } catch (error) {
        console.log('⚠️ Помилка створення таблиці:', error.message);
        return true; // Продовжуємо
    }
}

async function main() {
    try {
        console.log('🚀 Початок прямого SQL виправлення...');
        
        if (!supabaseUrl || !serviceKey) {
            console.log('❌ Відсутні змінні середовища SUPABASE_URL або SERVICE_KEY');
            return;
        }
        
        console.log(`🔗 URL: ${supabaseUrl}`);
        console.log(`🔑 Service Key: ${serviceKey.substring(0, 20)}...`);
        
        // 1. Створюємо таблицю якщо потрібно
        await createTableIfNeeded();
        
        // 2. Створюємо профіль
        const profileCreated = await createProfileDirectly();
        
        if (!profileCreated) {
            console.log('\n❌ Не вдалося створити профіль');
            return;
        }
        
        // 3. Перевіряємо профіль
        const profileExists = await verifyProfile();
        
        console.log('\n' + '='.repeat(40));
        
        if (profileExists) {
            console.log('🎉 ПРОФІЛЬ СТВОРЕНИЙ УСПІШНО!');
            console.log('\n🔐 ДАНІ ДЛЯ ВХОДУ:');
            console.log('📧 Email: <EMAIL>');
            console.log('🔑 Пароль: TempPass123!');
            console.log('\n✅ Тепер користувач може увійти в додаток');
        } else {
            console.log('⚠️ ПРОФІЛЬ НЕ ПІДТВЕРДЖЕНИЙ');
        }
        
    } catch (error) {
        console.log('\n❌ КРИТИЧНА ПОМИЛКА:', error.message);
    }
}

main();